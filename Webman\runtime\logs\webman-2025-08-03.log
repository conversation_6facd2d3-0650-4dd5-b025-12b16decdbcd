[2025-08-03 09:47:34] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/login [44.2450ms] [webman/log]
 [] []
[2025-08-03 09:47:34] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [301.042ms] [webman/log]
[POST]	array (
  'username' => '19815091111',
  'password' => '96e79218965eb72c92a549dd5a330112',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815091111') and `cs_company_admins`.`deleted_at` is null limit 1 [23.73 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = 18 and `cs_companys`.`deleted_at` is null limit 1 [7.36 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `login_time` = '2025-08-03 09:47:34', `cs_company_admins`.`updated_at` = '2025-08-03 09:47:34' where `id` = 18 [78.13 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 1 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [6.56 ms]
[SQL]	[connection:mysql] select `id` from `cs_menus` where (`menu_type` = 1 and `status` = 1) and `cs_menus`.`deleted_at` is null [5.29 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `menu_img`, `menu_sign`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `id` in (3055, 3056, 3057, 3058, 3059, 3060, 3061, 3062, 3063, 3064, 3065, 3066, 3067, 3068, 3069, 3070, 3071, 3072, 3073, 3074, 3075, 3076) and `cs_menus`.`deleted_at` is null order by `sort` asc [0.93 ms]
[Redis]	[connection:default] ...
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getProfile [0.05483ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"LoginController","action":"getProfile","route_key":"LoginController::getProfile"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [11.7580ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.61 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `expired_at` from `cs_companys` where (`id` = '18') and `cs_companys`.`deleted_at` is null limit 1 [1.04 ms]
[Redis]	[connection:default] Redis::setex('company_expired_at:18', '86400', '2025-08-11') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [1.09 ms]
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '18' and `cs_company_admins`.`deleted_at` is null limit 1 [0.55 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [0.82 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/overview [0.04506ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [0.02503ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"overview","route_key":"DashboardController::overview"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [37.1141ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.21 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [1.01 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [8.23 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.78 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.94 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [13.84 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.77 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.95 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [0.03004ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"revenueTrend","route_key":"DashboardController::revenueTrend"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [8.08119ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [0.91 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.75 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.5 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [0.02884ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"shopComparison","route_key":"DashboardController::shopComparison"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [4.67109ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [0.6 ms]
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/productRanking?period=today [0.03099ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"paymentAnalysis","route_key":"DashboardController::paymentAnalysis"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [6.00981ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [0.56 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.69 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/shopStatus [0.02384ms] [webman/log]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"productRanking","route_key":"DashboardController::productRanking"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [13.9780ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [0.57 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [9.9 ms]
 [] []
[2025-08-03 09:47:35] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"DashboardController","action":"shopStatus","route_key":"DashboardController::shopStatus"} []
[2025-08-03 09:47:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [17.7960ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [1.18 ms]
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.77 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [9.18 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.85 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.6 ms]
 [] []
[2025-08-03 09:47:45] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [0.03981ms] [webman/log]
 [] []
[2025-08-03 09:47:45] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"CsCompanyAdminRoleController","action":"index","route_key":"CsCompanyAdminRoleController::index"} []
[2025-08-03 09:47:45] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [8.76498ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.31 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [1.52 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.64 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [1.01 ms]
 [] []
[2025-08-03 09:47:46] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [0.04100ms] [webman/log]
 [] []
[2025-08-03 09:47:46] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"CsCompanyAdminRoleController","action":"edit","route_key":"CsCompanyAdminRoleController::edit"} []
[2025-08-03 09:47:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [6.19506ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [0.86 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.58 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [1.19 ms]
 [] []
[2025-08-03 09:48:07] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.06103ms] [webman/log]
 [] []
[2025-08-03 09:48:07] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.17285ms] [webman/log]
 [] []
[2025-08-03 09:48:10] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getUserInfo [0.04792ms] [webman/log]
 [] []
[2025-08-03 09:48:10] default.INFO: ROUTE_PERMISSION_MAPPING_NOT_FOUND {"controller":"LoginController","action":"getUserInfo","route_key":"LoginController::getUserInfo"} []
[2025-08-03 09:48:10] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getUserInfo [15.3667ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.35 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `id` = 18 and `company_id` = 18 and `status` = 1 and `cs_company_admins`.`deleted_at` is null limit 1 [1.08 ms]
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '18' and `cs_company_admins`.`deleted_at` is null limit 1 [0.73 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` in (18) and `cs_companys`.`deleted_at` is null [1.14 ms]
[SQL]	[connection:mysql] select * from `cs_product_versions` where `cs_product_versions`.`id` in (1) and `cs_product_versions`.`deleted_at` is null [5.56 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1) and `cs_company_admin_roles`.`deleted_at` is null [1.08 ms]
 [] []
[2025-08-03 09:51:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [26.7889ms] [webman/log]
 [] []
[2025-08-03 09:51:35] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [0.02503ms] [webman/log]
 [] []
[2025-08-03 09:51:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [143.326ms] [webman/log]
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [28.79 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [1.15 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.89 ms]
[Redis]	[connection:default] ...
 [] []
[2025-08-03 09:51:35] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [2.73299ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.75 ms]
 [] []
[2025-08-03 09:51:40] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/logout [0.06103ms] [webman/log]
 [] []
[2025-08-03 09:51:40] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/logout [1.62506ms] [webman/log]
[POST]	array (
)
[Redis]	[connection:default] Redis::exists('company:c181e6f3b834a11c62dd79d7d7277f43') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:c181e6f3b834a11c62dd79d7d7277f43') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[Redis]	[connection:default] Redis::del('company:c181e6f3b834a11c62dd79d7d7277f43') (0.2 ms)
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/login [0.06389ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 POST 127.0.0.1:8787/company/Login/login [36.4098ms] [webman/log]
[POST]	array (
  'username' => '19815091112',
  'password' => '96e79218965eb72c92a549dd5a330112',
)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`username` = '19815091112') and `cs_company_admins`.`deleted_at` is null limit 1 [1.02 ms]
[SQL]	[connection:mysql] select * from `cs_companys` where `cs_companys`.`id` = 18 and `cs_companys`.`deleted_at` is null limit 1 [1.04 ms]
[SQL]	[connection:mysql] update `cs_company_admins` set `login_time` = '2025-08-03 09:51:50', `cs_company_admins`.`updated_at` = '2025-08-03 09:51:50' where `id` = 20 [23.11 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [0.72 ms]
[Redis]	[connection:default] Redis::setex('company:cd633d061f72ec6f1f28b840b848c31b', '2592000', '20-5-18') (0.19 ms)
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `menu_img`, `menu_sign`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `id` in ('3059', '3060', '3061', '3062', '3063') and `cs_menus`.`deleted_at` is null order by `sort` asc [1.97 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Login/getProfile [0.10514ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [4.09197ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.26 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [1.15 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.77 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/overview [0.03886ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [0.03790ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [13.1828ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [1.53 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.69 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.91 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.65 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [1.23 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.81 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.92 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [0.12683ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [8.59403ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.87 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [1.03 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.82 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.75 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.79 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.76 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [0.03099ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.90481ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.78 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.64 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/productRanking?period=today [0.03314ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [4.14800ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.63 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/Dashboard/shopStatus [0.04410ms] [webman/log]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.85808ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.19 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [1.42 ms]
 [] []
[2025-08-03 09:51:50] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [7.59911ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.96 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.76 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.51 ms]
 [] []
[2025-08-03 09:52:08] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.09698ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [1.26 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.63 ms]
 [] []
[2025-08-03 09:52:09] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdmin/index?page=1&page_size=10&sort_field=id&sort=desc [5.28907ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.79 ms]
[SQL]	[connection:mysql] select `id`, `role_name` as `relation_name` from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (1, 5) and `cs_company_admin_roles`.`deleted_at` is null [0.8 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admins` where (`company_id` = '18') and `cs_company_admins`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-03 09:52:09] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/getRoleList [1.75905ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select `id`, `role_name` from `cs_company_admin_roles` where (`id` = 1 or `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.57 ms]
 [] []
[2025-08-03 09:52:10] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.44896ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.81 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.6 ms]
 [] []
[2025-08-03 09:52:12] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/edit?id=5 [5.19800ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` = 5 and `cs_company_admin_roles`.`deleted_at` is null limit 1 [1 ms]
[SQL]	[connection:mysql] select `id`, `pid`, `menu_name`, `is_show` from `cs_menus` where `menu_type` = 1 and `status` = 1 and `cs_menus`.`deleted_at` is null order by `sort` asc [0.92 ms]
 [] []
[2025-08-03 10:04:00] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.00312ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.72 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-03 10:04:00] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.01909ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.71 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.7 ms]
 [] []
[2025-08-03 10:04:11] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [4.41002ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.28 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.19 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [1.18 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.71 ms]
 [] []
[2025-08-03 10:04:11] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [4.22191ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.91 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.76 ms]
 [] []
[2025-08-03 10:04:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.76296ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.93 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:04:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.76820ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.83 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-03 10:04:42] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.85410ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.67 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.49 ms]
 [] []
[2025-08-03 10:04:42] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.59511ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.76 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.75 ms]
 [] []
[2025-08-03 10:04:54] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.69000ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.84 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.57 ms]
 [] []
[2025-08-03 10:04:54] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.20506ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.71 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-03 10:06:08] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.26204ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.89 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.53 ms]
 [] []
[2025-08-03 10:06:08] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.56411ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.86 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-03 10:06:19] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.10802ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.32 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.46 ms]
 [] []
[2025-08-03 10:06:19] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.30400ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.67 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.54 ms]
 [] []
[2025-08-03 10:06:27] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getCustomerServiceInfo [0.19311ms] [webman/log]
 [] []
[2025-08-03 10:06:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.23104ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:06:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsCompanyAdminRole/index?page=1&page_size=10&sort_field=id&sort=desc [3.49402ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null order by `id` desc limit 10 offset 0 [0.83 ms]
[SQL]	[connection:mysql] select count(`id`) as aggregate from `cs_company_admin_roles` where (`id` > 1 and `company_id` = '18') and `cs_company_admin_roles`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.04412ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.72 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [8.07094ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.16 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.78 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.66189ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.65 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.6 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.51786ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.64 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [4.27412ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.92 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.09991ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.94 ms]
 [] []
[2025-08-03 10:06:47] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [7.01212ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.66 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.78091ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.43 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [8.16392ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.44 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.7 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.67 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.83591ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [1.24 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.21698ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.16 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.54 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [5.43594ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.44 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.4 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.45 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [2.70104ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.44 ms]
 [] []
[2025-08-03 10:08:30] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.49094ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.69 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.71892ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.61 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.43 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [7.64393ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.7 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.61 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [2.97307ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.89 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.42702ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.61 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.11708ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.23 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.47 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [5.94806ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.62 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.43 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.48 ms]
 [] []
[2025-08-03 10:08:40] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.68406ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.94 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.66599ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.62 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.42 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [7.66706ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.64 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.86595ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [1.16 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.99804ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.62 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.71 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.04388ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.58 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [5.96809ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.57 ms]
 [] []
[2025-08-03 10:08:58] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.10899ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.83598ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.7 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.46 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [7.79891ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.69 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.89 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.58819ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.72 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.94 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.08108ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.59 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [2.85601ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.44 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [5.35607ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.44 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.43 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.4 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.46 ms]
 [] []
[2025-08-03 10:09:56] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [5.42497ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.42 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.69699ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.66 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [7.28297ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.09 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.43 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.46 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.56 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [3.16309ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.25 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.83 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.47995ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.7 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.61 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [2.72417ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.45 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.45 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [5.83505ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.48 ms]
 [] []
[2025-08-03 10:11:01] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [5.60212ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.09 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.47 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.48 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [4.01806ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.3 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.16 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [1.09 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.74 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [9.49001ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.84 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.7 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.73 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.65 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.81 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [2.75707ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.23 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.52 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.82 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.20982ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.59 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.39198ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.59 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [8.71491ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.62 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.8 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.95 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.78 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:14:46] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [7.89403ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.25 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.21 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.7 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.62 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.62 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.73 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.69 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.57 ms]
 [] []
[2025-08-03 10:15:23] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [3.86214ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.23 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.96 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.66 ms]
 [] []
[2025-08-03 10:15:49] default.INFO: 127.0.0.1 OPTIONS 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [0.04315ms] [webman/log]
 [] []
[2025-08-03 10:15:49] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [6.97422ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.75 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.81 ms]
 [] []
[2025-08-03 10:16:16] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.34803ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.76 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.65 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [11.4779ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.29 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.21 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.18 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.91 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.82 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.98 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.73 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.75 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.79 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [2.63190ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.84 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.23390ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.1 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.63 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.38697ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.72 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [7.48085ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.2 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.17 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.73 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.64 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.6 ms]
 [] []
[2025-08-03 10:16:17] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [6.80613ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.48 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.62 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.64 ms]
 [] []
[2025-08-03 10:16:18] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.98612ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.24 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.72 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.68 ms]
 [] []
[2025-08-03 10:16:31] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.35400ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.78 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.87 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [7.86900ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.19 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.65 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.63 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [2.61592ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.85 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.35192ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.61 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.26395ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.57 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.68597ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.63 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.6 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:16:32] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [6.82115ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.15 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.62 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.51 ms]
 [] []
[2025-08-03 10:16:41] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [3.96704ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.22 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.67 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/overview [8.00490ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.72 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and month(`created_at`) = '08' and year(`created_at`) = '2025' and `cs_shops`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.67 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` in (28, 29) and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.65 ms]
[SQL]	[connection:mysql] select count(distinct `user_id`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and month(`created_at`) = '08' and year(`created_at`) = '2025' and `user_id` > 0 and `cs_orders`.`deleted_at` is null [0.62 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/productRanking?period=today [2.52079ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.49 ms]
[SQL]	[connection:mysql] select 
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                 from `cs_order_goodss` as `og` inner join `cs_orders` as `o` on `og`.`order_id` = `o`.`id` inner join `cs_goods` as `g` on `og`.`goods_id` = `g`.`id` where `o`.`shop_id` in (28, 29) and `o`.`order_status` >= 2 and date(`o`.`created_at`) = '2025-08-03' group by `g`.`id`, `g`.`goods_name` order by `sales` desc limit 5 [0.79 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/paymentAnalysis?period=today [3.11303ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.1 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select payment_type, SUM(real_pay_money) as amount, COUNT(*) as count from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `payment_type` is not null and `payment_type` > 0 and `cs_orders`.`deleted_at` is null group by `payment_type` [0.6 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopComparison?period=today [3.23820ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.16 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.12 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.11 ms)
[SQL]	[connection:mysql] select `id`, `shop_name` from `cs_shops` where `company_id` = '18' and `status` = 1 and `cs_shops`.`deleted_at` is null [0.53 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.56 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.51 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/revenueTrend?period=today [6.70003ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.13 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.74 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '00:00' and time(`created_at`) < '04:00' and `cs_orders`.`deleted_at` is null [0.61 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '04:00' and time(`created_at`) < '08:00' and `cs_orders`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '08:00' and time(`created_at`) < '12:00' and `cs_orders`.`deleted_at` is null [0.54 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '12:00' and time(`created_at`) < '16:00' and `cs_orders`.`deleted_at` is null [0.51 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '16:00' and time(`created_at`) < '20:00' and `cs_orders`.`deleted_at` is null [0.59 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` in (28, 29) and `order_status` >= 2 and date(`created_at`) = '2025-08-03' and time(`created_at`) >= '20:00' and time(`created_at`) < '24:00' and `cs_orders`.`deleted_at` is null [0.79 ms]
 [] []
[2025-08-03 10:16:43] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Dashboard/shopStatus [7.13896ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.18 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.14 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.12 ms)
[SQL]	[connection:mysql] select `id`, `shop_name`, `status`, `updated_at` from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.55 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.57 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 28 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.5 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 28 and `inv`.`current_stock` < 'inv.min_stock' [0.59 ms]
[SQL]	[connection:mysql] select sum(`real_pay_money`) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `order_status` >= 2 and `cs_orders`.`deleted_at` is null [0.96 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_orders` where `shop_id` = 29 and date(`created_at`) = '2025-08-03' and `cs_orders`.`deleted_at` is null [0.66 ms]
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_inventory` as `inv` inner join `cs_goods` as `g` on `inv`.`goods_id` = `g`.`id` where `g`.`shop_id` = 29 and `inv`.`current_stock` < 'inv.min_stock' [0.49 ms]
 [] []
[2025-08-03 10:18:20] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.01782ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.25 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.66 ms]
 [] []
[2025-08-03 10:23:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/Login/getProfile [2.96592ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.17 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.11 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.13 ms)
[SQL]	[connection:mysql] select * from `cs_company_admins` where `cs_company_admins`.`id` = '20' and `cs_company_admins`.`deleted_at` is null limit 1 [0.71 ms]
[SQL]	[connection:mysql] select * from `cs_company_admin_roles` where `cs_company_admin_roles`.`id` in (5) and `cs_company_admin_roles`.`deleted_at` is null [0.52 ms]
 [] []
[2025-08-03 10:23:24] default.INFO: 127.0.0.1 GET 127.0.0.1:8787/company/CsShop/index?page=1&limit=15&shop_name=&nickname=&mobile= [4.30512ms] [webman/log]
[Redis]	[connection:default] Redis::exists('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company:cd633d061f72ec6f1f28b840b848c31b') (0.15 ms)
[Redis]	[connection:default] Redis::get('company_expired_at:18') (0.14 ms)
[SQL]	[connection:mysql] select count(*) as aggregate from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null [0.58 ms]
[SQL]	[connection:mysql] select * from `cs_shops` where `company_id` = '18' and `cs_shops`.`deleted_at` is null order by `id` desc limit 15 offset 0 [0.75 ms]
 [] []
