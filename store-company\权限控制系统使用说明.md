# 企业管理系统权限控制功能使用说明

## 功能概述

本系统已实现基于角色的权限控制（RBAC），可以根据登录用户的角色权限动态控制菜单显示和操作按钮的可见性。

## 主要特性

### 1. 菜单权限控制
- 根据用户权限动态显示/隐藏侧边栏菜单项
- 支持多级菜单的权限控制
- 无权限的菜单项将完全隐藏

### 2. 操作按钮权限控制
- 页面内的操作按钮根据权限显示/隐藏
- 支持新增、编辑、删除等操作的细粒度权限控制

### 3. 路由权限守卫
- 防止用户直接通过URL访问无权限的页面
- 无权限时自动重定向到首页

### 4. 权限指令支持
- 提供 `v-permission` 指令，方便在模板中使用
- 支持单个权限和多个权限的检查

## 权限标识定义

系统使用以下权限标识：

```typescript
// 门店管理
shop_manage: '门店管理'

// 管理员管理
admin_manage: '管理员管理'
admin_add: '新增管理员'
admin_edit: '编辑管理员'
admin_delete: '删除管理员'

// 角色管理
role_manage: '角色管理'
role_add: '新增角色'
role_edit: '编辑角色'
role_delete: '删除角色'
role_permission_assign: '分配权限'
```

## 使用方法

### 1. 在组件中使用权限检查

```vue
<script setup lang="ts">
import { usePermission } from '@/composables/usePermission'

const { hasPermission } = usePermission()
</script>

<template>
  <!-- 使用 v-if 条件渲染 -->
  <button v-if="hasPermission('role_add')" @click="addRole">
    新增角色
  </button>
  
  <!-- 使用权限指令 -->
  <button v-permission="'role_edit'" @click="editRole">
    编辑角色
  </button>
  
  <!-- 多个权限（任意一个即可） -->
  <button v-permission="['role_add', 'role_edit']" @click="manageRole">
    管理角色
  </button>
</template>
```

### 2. 在路由中配置权限

```typescript
{
  path: '/shops',
  component: () => import('@/views/Shops.vue'),
  meta: { 
    title: '门店管理',
    requiresAuth: true,
    requiredPermission: 'shop_manage' // 需要的权限
  }
}
```

### 3. 权限检查API

```typescript
import { usePermission } from '@/composables/usePermission'

const { 
  hasPermission,        // 检查单个权限
  hasAnyPermission,     // 检查是否有任意一个权限
  hasAllPermissions,    // 检查是否有所有权限
  canAccessMenu,        // 检查菜单访问权限
  canAccessRoute        // 检查路由访问权限
} = usePermission()

// 使用示例
if (hasPermission('role_add')) {
  // 有新增角色权限
}

if (hasAnyPermission(['role_add', 'role_edit'])) {
  // 有新增或编辑权限
}
```

## 角色权限配置示例

### 超级管理员
拥有所有权限：
- shop_manage
- admin_manage, admin_add, admin_edit, admin_delete
- role_manage, role_add, role_edit, role_delete, role_permission_assign

### 门店管理员
只有门店管理权限：
- shop_manage

### 角色管理员
只有角色相关权限：
- role_manage, role_add, role_edit, role_delete, role_permission_assign



## 开发环境配置

在开发环境下，系统会自动使用模拟权限数据，无需配置后端API。模拟数据定义在 `src/utils/mockPermissions.ts` 文件中。

## 生产环境配置

在生产环境下，需要确保后端API返回正确的权限数据：

### 登录接口返回格式
```json
{
  "token": "...",
  "id": 1,
  "username": "admin",
  "role_id": 1,
  "role_text": "超级管理员",
  "permissions": ["shop_manage", "admin_manage", "role_manage"],
  "menu": [...]
}
```

### 权限接口
需要实现 `/company/Login/getUserPermissions` 接口，返回：
```json
{
  "permissions": ["shop_manage", "admin_manage", "role_manage"],
  "menus": [...]
}
```

## 注意事项

1. **权限标识一致性**：前端权限标识必须与后端保持一致
2. **缓存更新**：权限变更后需要重新登录或刷新权限缓存
3. **安全性**：前端权限控制仅用于UI展示，后端必须进行权限验证
4. **测试覆盖**：建议为每个权限点编写测试用例

## 故障排除

### 权限不生效
1. 检查用户是否已登录
2. 确认权限标识拼写正确
3. 查看浏览器控制台是否有错误信息
4. 检查localStorage中的用户信息是否包含权限数据

### 菜单不显示
1. 确认权限标识配置正确
2. 检查菜单组件中的权限检查逻辑
3. 验证用户角色是否有对应权限

### 路由访问被拦截
1. 检查路由配置中的 `requiredPermission` 字段
2. 确认用户有对应的权限
3. 查看路由守卫的执行逻辑
