import type { App, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令
 * 用法：v-permission="'permission_key'" 或 v-permission="['permission1', 'permission2']"
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  }
}

/**
 * 检查权限并控制元素显示
 */
function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding
  const authStore = useAuthStore()
  
  if (!value) {
    // 没有权限要求，显示元素
    el.style.display = ''
    return
  }

  const userPermissions = authStore.user?.permissions || []
  let hasPermission = false

  if (typeof value === 'string') {
    // 单个权限
    hasPermission = userPermissions.includes(value)
  } else if (Array.isArray(value)) {
    // 多个权限，只要有一个即可
    hasPermission = value.some(permission => userPermissions.includes(permission))
  }

  if (hasPermission) {
    el.style.display = ''
  } else {
    el.style.display = 'none'
  }
}

/**
 * 安装权限指令
 */
export function setupPermissionDirective(app: App) {
  app.directive('permission', permissionDirective)
}

/**
 * 权限检查工具函数
 */
export class PermissionUtils {
  /**
   * 检查是否有权限
   */
  static hasPermission(permission: string): boolean {
    const authStore = useAuthStore()
    return authStore.user?.permissions?.includes(permission) || false
  }

  /**
   * 检查是否有任意一个权限
   */
  static hasAnyPermission(permissions: string[]): boolean {
    const authStore = useAuthStore()
    const userPermissions = authStore.user?.permissions || []
    return permissions.some(permission => userPermissions.includes(permission))
  }

  /**
   * 检查是否有所有权限
   */
  static hasAllPermissions(permissions: string[]): boolean {
    const authStore = useAuthStore()
    const userPermissions = authStore.user?.permissions || []
    return permissions.every(permission => userPermissions.includes(permission))
  }

  /**
   * 获取用户权限列表
   */
  static getUserPermissions(): string[] {
    const authStore = useAuthStore()
    return authStore.user?.permissions || []
  }
}
