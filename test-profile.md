# ProfileDialog 功能测试说明

## 已完成的功能

### 后端API接口
1. **获取个人资料**: `GET /company/Login/getProfile`
   - 返回当前用户的个人信息（昵称、用户名、角色等）
   
2. **更新个人资料**: `POST /company/Login/updateProfile`
   - 支持修改昵称和密码
   - 昵称：必填，2-20个字符
   - 密码：可选，6-20个字符

### 前端集成
1. **API服务层**
   - 在 `authApi.ts` 中添加了 `getProfile()` 和 `updateProfile()` 方法
   - 更新了类型定义：`ProfileData` 和 `ProfileForm`

2. **ProfileDialog组件**
   - 集成真实API调用
   - 支持动态加载用户数据
   - 实现表单验证和错误处理
   - 密码字段为可选项

## 测试步骤

### 前置条件
1. 确保后端Webman服务运行在 http://localhost:8787
2. 确保前端Vue服务运行在 http://localhost:5173

### 测试流程
1. 登录系统
2. 点击头部的用户头像或个人中心按钮
3. 打开个人资料对话框
4. 验证数据是否正确加载
5. 修改昵称并保存
6. 测试密码修改功能（可选）

### 验证要点
- [ ] 个人资料数据正确加载
- [ ] 昵称修改功能正常
- [ ] 密码修改功能正常（如果填写）
- [ ] 表单验证工作正常
- [ ] 错误处理显示友好消息
- [ ] 保存成功后自动关闭对话框

## API路由配置

需要确保后端路由配置中包含以下接口：
```php
// 在 config/route.php 或相应路由文件中
Route::get('/company/Login/getProfile', [LoginController::class, 'getProfile']);
Route::post('/company/Login/updateProfile', [LoginController::class, 'updateProfile']);
```

## 注意事项
1. 原始管理员修改密码时会同步更新公司表中的密码
2. 密码字段留空表示不修改密码
3. 昵称修改会同步更新认证存储中的用户信息