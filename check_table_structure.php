<?php
/**
 * 检查数据库表结构脚本
 */

try {
    $pdo = new PDO(
        'mysql:host=127.0.0.1;dbname=shop_sass_xly;charset=utf8mb4',
        'root',
        'root'
    );
    
    echo "=== 检查数据库表结构 ===\n\n";
    
    // 检查cs_order_goodss表结构
    echo "1. cs_order_goodss 表结构:\n";
    $stmt = $pdo->prepare("DESCRIBE cs_order_goodss");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasDeletedAt = false;
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        if ($column['Field'] === 'deleted_at') {
            $hasDeletedAt = true;
        }
    }
    
    echo "\ndeleted_at字段: " . ($hasDeletedAt ? "✅ 存在" : "❌ 不存在") . "\n\n";
    
    // 如果没有deleted_at字段，提供添加建议
    if (!$hasDeletedAt) {
        echo "建议添加deleted_at字段的SQL:\n";
        echo "ALTER TABLE `cs_order_goodss` ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL COMMENT '删除时间';\n\n";
    }
    
    // 检查cs_orders表结构
    echo "2. cs_orders 表结构:\n";
    $stmt = $pdo->prepare("DESCRIBE cs_orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasCompanyId = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'company_id') {
            $hasCompanyId = true;
            echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']} ✅\n";
        } elseif (in_array($column['Field'], ['id', 'shop_id', 'payment_type', 'order_status', 'real_pay_money', 'created_at'])) {
            echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        }
    }
    
    echo "\ncompany_id字段: " . ($hasCompanyId ? "✅ 存在" : "❌ 不存在") . "\n\n";
    
    // 检查cs_payment_methods表
    echo "3. cs_payment_methods 表结构:\n";
    $stmt = $pdo->prepare("DESCRIBE cs_payment_methods");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        if (in_array($column['Field'], ['id', 'shop_id', 'payment_method_name', 'status'])) {
            echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
        }
    }
    
    // 检查数据样本
    echo "\n4. 数据样本检查:\n";
    
    // 检查订单数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cs_orders WHERE order_status >= 2");
    $stmt->execute();
    $orderCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "有效订单数量: {$orderCount}\n";
    
    // 检查订单商品数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cs_order_goodss");
    $stmt->execute();
    $orderGoodsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "订单商品记录数量: {$orderGoodsCount}\n";
    
    // 检查支付方式数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cs_payment_methods WHERE status = 1");
    $stmt->execute();
    $paymentMethodCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "有效支付方式数量: {$paymentMethodCount}\n";
    
    // 检查今日订单
    $stmt = $pdo->prepare("SELECT COUNT(*) as count, SUM(real_pay_money) as amount FROM cs_orders WHERE DATE(created_at) = CURDATE() AND order_status >= 2");
    $stmt->execute();
    $todayData = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "今日订单: {$todayData['count']} 笔, 金额: ¥{$todayData['amount']}\n";
    
} catch (Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}
