<?php

namespace app\service;

use app\model\CsMenu;
use app\model\CsCompanyAdminRole;
use app\model\CsCompanyAdmin;
use support\Request;
use support\Log;

/**
 * 权限验证服务
 * 
 * 提供多租户SaaS系统完整的权限验证功能
 * 支持缓存优化、批量验证、路由自动映射等企业级特性
 */
class PermissionService extends BaseService
{
    /**
     * 权限缓存服务实例
     */
    private PermissionCacheService $cacheService;

    /**
     * 路由权限映射服务实例
     */
    private RoutePermissionService $routeService;

    public function __construct()
    {
        $this->cacheService = new PermissionCacheService();
        $this->routeService = new RoutePermissionService();
    }

    /**
     * 核心权限检查方法
     * 
     * @param int $companyId 公司ID
     * @param int $adminId 管理员ID
     * @param int $roleId 角色ID
     * @param string $menuSign 菜单权限标识
     * @return bool 是否有权限
     */
    public function checkPermission(int $companyId, int $adminId, int $roleId, string $menuSign): bool
    {
        try {
            // 数据安全验证：确保多租户数据隔离
            if (!$this->validateTenantAccess($companyId, $adminId)) {
                Log::info('PERMISSION_DENIED', [
                    'reason' => 'tenant_access_violation',
                    'company_id' => $companyId,
                    'admin_id' => $adminId,
                    'menu_sign' => $menuSign
                ]);
                return false;
            }

            // 超级管理员（role_id = 1）拥有所有权限
            if ($roleId === 1) {
                return true;
            }

            // 从缓存获取用户权限
            $userPermissions = $this->cacheService->getUserPermissions($companyId, $adminId);
            
            // 检查是否有对应的菜单权限
            return $this->hasMenuPermission($userPermissions, $menuSign);

        } catch (\Exception $e) {
            Log::info('PERMISSION_CHECK_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'admin_id' => $adminId,
                'menu_sign' => $menuSign
            ]);
            
            // 权限检查异常时，默认拒绝访问
            return false;
        }
    }

    /**
     * 获取用户完整权限列表
     * 
     * @param int $companyId 公司ID
     * @param int $adminId 管理员ID
     * @return array 权限列表
     */
    public function getUserPermissions(int $companyId, int $adminId): array
    {
        try {
            return $this->cacheService->getUserPermissions($companyId, $adminId);
        } catch (\Exception $e) {
            Log::info('GET_USER_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'admin_id' => $adminId
            ]);
            return [];
        }
    }

    /**
     * 批量权限验证
     * 
     * @param int $companyId 公司ID
     * @param int $adminId 管理员ID
     * @param int $roleId 角色ID
     * @param array $menuSigns 菜单权限标识数组
     * @return array 权限检查结果映射
     */
    public function checkMultiplePermissions(int $companyId, int $adminId, int $roleId, array $menuSigns): array
    {
        $results = [];
        
        try {
            // 数据安全验证
            if (!$this->validateTenantAccess($companyId, $adminId)) {
                // 全部权限拒绝
                return array_fill_keys($menuSigns, false);
            }

            // 超级管理员拥有所有权限
            if ($roleId === 1) {
                return array_fill_keys($menuSigns, true);
            }

            // 获取用户权限（利用缓存）
            $userPermissions = $this->cacheService->getUserPermissions($companyId, $adminId);
            
            // 批量检查权限
            foreach ($menuSigns as $menuSign) {
                $results[$menuSign] = $this->hasMenuPermission($userPermissions, $menuSign);
            }

        } catch (\Exception $e) {
            Log::info('BATCH_PERMISSION_CHECK_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'admin_id' => $adminId,
                'menu_signs' => $menuSigns
            ]);
            
            // 异常时全部拒绝
            $results = array_fill_keys($menuSigns, false);
        }

        return $results;
    }

    /**
     * 路由自动权限检查
     * 
     * @param Request $request 请求对象
     * @return bool 是否有权限访问当前路由
     */
    public function checkRoutePermission(Request $request): bool
    {
        try {
            // 获取路由对应的权限标识
            $menuSign = $this->routeService->getMenuSignByRoute(
                $request->controller,
                $request->action
            );

            // 如果路由不需要权限检查，直接通过
            if (empty($menuSign)) {
                return true;
            }

            // 执行权限检查
            return $this->checkPermission(
                $request->company_id ?? 0,
                $request->admin_id ?? 0,
                $request->role_id ?? 0,
                $menuSign
            );

        } catch (\Exception $e) {
            Log::info('ROUTE_PERMISSION_CHECK_ERROR', [
                'error' => $e->getMessage(),
                'controller' => $request->controller ?? '',
                'action' => $request->action ?? '',
                'company_id' => $request->company_id ?? 0,
                'admin_id' => $request->admin_id ?? 0
            ]);
            
            return false;
        }
    }

    /**
     * 获取用户可访问的菜单列表
     * 
     * @param int $companyId 公司ID
     * @param int $adminId 管理员ID
     * @param int $roleId 角色ID
     * @param int $menuType 菜单类型
     * @return array 可访问的菜单树
     */
    public function getUserAccessibleMenus(int $companyId, int $adminId, int $roleId, int $menuType = 1): array
    {
        try {
            // 超级管理员返回所有菜单
            if ($roleId === 1) {
                return $this->getAllMenus($menuType);
            }

            // 获取用户权限
            $userPermissions = $this->cacheService->getUserPermissions($companyId, $adminId);
            
            // 构建权限菜单树
            return $this->buildAccessibleMenuTree($userPermissions, $menuType);

        } catch (\Exception $e) {
            Log::info('GET_USER_MENUS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'admin_id' => $adminId,
                'menu_type' => $menuType
            ]);
            
            return [];
        }
    }

    /**
     * 权限更新后清除缓存
     * 
     * @param int $companyId 公司ID
     * @param int|null $adminId 管理员ID（null表示清除整个公司缓存）
     * @param int|null $roleId 角色ID（null表示不按角色清除）
     */
    public function clearPermissionCache(int $companyId, ?int $adminId = null, ?int $roleId = null): void
    {
        try {
            if ($adminId) {
                // 清除指定用户权限缓存
                $this->cacheService->invalidateUserPermissions($companyId, $adminId);
            } elseif ($roleId) {
                // 清除指定角色的所有用户权限缓存
                $this->cacheService->invalidateRolePermissions($companyId, $roleId);
            } else {
                // 清除整个公司的权限缓存
                $this->cacheService->invalidateCompanyPermissions($companyId);
            }

            Log::info('PERMISSION_CACHE_CLEARED', [
                'company_id' => $companyId,
                'admin_id' => $adminId,
                'role_id' => $roleId
            ]);

        } catch (\Exception $e) {
            Log::info('CLEAR_PERMISSION_CACHE_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'admin_id' => $adminId,
                'role_id' => $roleId
            ]);
        }
    }

    /**
     * 验证租户访问权限
     * 
     * 确保用户只能访问自己公司的数据
     * 
     * @param int $companyId 公司ID
     * @param int $adminId 管理员ID
     * @return bool 是否通过验证
     */
    private function validateTenantAccess(int $companyId, int $adminId): bool
    {
        if ($companyId <= 0 || $adminId <= 0) {
            return false;
        }

        // 验证管理员是否属于该公司
        $admin = CsCompanyAdmin::where('id', $adminId)
            ->where('company_id', $companyId)
            ->where('status', 1)
            ->first();

        return !empty($admin);
    }

    /**
     * 检查是否有指定菜单权限
     * 
     * @param array $userPermissions 用户权限列表
     * @param string $menuSign 菜单权限标识
     * @return bool 是否有权限
     */
    private function hasMenuPermission(array $userPermissions, string $menuSign): bool
    {
        // 检查是否在权限列表中
        return in_array($menuSign, $userPermissions['menu_signs'] ?? []);
    }

    /**
     * 获取指定类型的所有菜单
     * 
     * @param int $menuType 菜单类型
     * @return array 菜单树
     */
    private function getAllMenus(int $menuType): array
    {
        return CsMenu::where('menu_type', $menuType)
            ->where('status', 1)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }

    /**
     * 构建用户可访问的菜单树
     * 
     * @param array $userPermissions 用户权限
     * @param int $menuType 菜单类型
     * @return array 菜单树
     */
    private function buildAccessibleMenuTree(array $userPermissions, int $menuType): array
    {
        $permissionIds = $userPermissions['menu_ids'] ?? [];
        
        if (empty($permissionIds)) {
            return [];
        }

        return CsMenu::whereIn('id', $permissionIds)
            ->where('menu_type', $menuType)
            ->where('status', 1)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }
}