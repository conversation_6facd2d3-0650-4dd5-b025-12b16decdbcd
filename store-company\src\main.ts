import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import { useAuthStore } from './stores/auth'
import { setupPermissionDirective } from './utils/permission'
import 'element-plus/dist/index.css'
import './styles/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)

// 注册权限指令
setupPermissionDirective(app)

// 初始化认证状态
const authStore = useAuthStore()
authStore.initUserInfo()

// 开发环境下加载API测试工具
if (import.meta.env.DEV) {
  import('./services/test').then(({ runAllTests }) => {
    console.log('🚀 Store-Company 前端请求工具已加载')
    console.log('📖 使用说明请查看: src/services/README.md')
    console.log('🧪 运行 window.testApi.runAllTests() 来测试API工具')
  })
}

app.mount('#app')