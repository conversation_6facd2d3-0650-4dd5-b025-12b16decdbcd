<?php

namespace app\service;

use app\model\CsMenu;
use app\model\CsCompanyAdminRole;
use app\model\CsCompanyAdmin;
use support\Redis;
use support\Log;

/**
 * 权限缓存服务
 * 
 * 提供高性能的权限数据缓存机制
 * 包含多层缓存策略、智能失效机制和缓存预热功能
 */
class PermissionCacheService
{
    /**
     * 缓存TTL配置（秒）
     */
    private const USER_PERMISSIONS_TTL = 3600;      // 用户权限缓存 1小时
    private const ROLE_PERMISSIONS_TTL = 7200;      // 角色权限缓存 2小时
    private const MENU_MAPPING_TTL = 86400;         // 菜单映射缓存 24小时
    private const ROUTE_MAPPING_TTL = 86400;        // 路由映射缓存 24小时

    /**
     * 缓存Key前缀
     */
    private const USER_PERMISSIONS_PREFIX = 'user_permissions';
    private const ROLE_PERMISSIONS_PREFIX = 'role_permissions';
    private const MENU_MAPPING_PREFIX = 'menu_mapping';
    private const ROUTE_MAPPING_PREFIX = 'route_mapping';

    /**
     * 获取用户权限（带缓存）
     * 
     * @param int $companyId 公司ID
     * @param int $userId 用户ID
     * @return array 用户权限数据
     */
    public function getUserPermissions(int $companyId, int $userId): array
    {
        $cacheKey = $this->getUserPermissionsCacheKey($companyId, $userId);
        
        try {
            // 尝试从缓存获取
            $cachedData = Redis::get($cacheKey);
            if ($cachedData !== false) {
                $permissions = json_decode($cachedData, true);
                if (is_array($permissions)) {
                    return $permissions;
                }
            }

            // 缓存未命中，从数据库加载
            $permissions = $this->loadUserPermissionsFromDB($companyId, $userId);
            
            // 写入缓存
            Redis::setex($cacheKey, self::USER_PERMISSIONS_TTL, json_encode($permissions));
            
            Log::info('USER_PERMISSIONS_CACHED', [
                'company_id' => $companyId,
                'user_id' => $userId,
                'cache_key' => $cacheKey
            ]);

            return $permissions;

        } catch (\Exception $e) {
            Log::info('GET_USER_PERMISSIONS_CACHE_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'user_id' => $userId,
                'cache_key' => $cacheKey
            ]);

            // 缓存异常时直接查数据库
            return $this->loadUserPermissionsFromDB($companyId, $userId);
        }
    }

    /**
     * 获取角色权限（带缓存）
     * 
     * @param int $companyId 公司ID
     * @param int $roleId 角色ID
     * @return array 角色权限数据
     */
    public function getRolePermissions(int $companyId, int $roleId): array
    {
        $cacheKey = $this->getRolePermissionsCacheKey($companyId, $roleId);
        
        try {
            // 尝试从缓存获取
            $cachedData = Redis::get($cacheKey);
            if ($cachedData !== false) {
                $permissions = json_decode($cachedData, true);
                if (is_array($permissions)) {
                    return $permissions;
                }
            }

            // 缓存未命中，从数据库加载
            $permissions = $this->loadRolePermissionsFromDB($companyId, $roleId);
            
            // 写入缓存
            Redis::setex($cacheKey, self::ROLE_PERMISSIONS_TTL, json_encode($permissions));
            
            Log::info('ROLE_PERMISSIONS_CACHED', [
                'company_id' => $companyId,
                'role_id' => $roleId,
                'cache_key' => $cacheKey
            ]);

            return $permissions;

        } catch (\Exception $e) {
            Log::info('GET_ROLE_PERMISSIONS_CACHE_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'role_id' => $roleId,
                'cache_key' => $cacheKey
            ]);

            return $this->loadRolePermissionsFromDB($companyId, $roleId);
        }
    }

    /**
     * 获取菜单权限映射（带缓存）
     * 
     * @param int $menuType 菜单类型
     * @return array 菜单权限映射
     */
    public function getMenuMapping(int $menuType): array
    {
        $cacheKey = $this->getMenuMappingCacheKey($menuType);
        
        try {
            // 尝试从缓存获取
            $cachedData = Redis::get($cacheKey);
            if ($cachedData !== false) {
                $mapping = json_decode($cachedData, true);
                if (is_array($mapping)) {
                    return $mapping;
                }
            }

            // 缓存未命中，从数据库加载
            $mapping = $this->loadMenuMappingFromDB($menuType);
            
            // 写入缓存
            Redis::setex($cacheKey, self::MENU_MAPPING_TTL, json_encode($mapping));
            
            return $mapping;

        } catch (\Exception $e) {
            Log::info('GET_MENU_MAPPING_CACHE_ERROR', [
                'error' => $e->getMessage(),
                'menu_type' => $menuType,
                'cache_key' => $cacheKey
            ]);

            return $this->loadMenuMappingFromDB($menuType);
        }
    }

    /**
     * 失效用户权限缓存
     * 
     * @param int $companyId 公司ID
     * @param int $userId 用户ID
     */
    public function invalidateUserPermissions(int $companyId, int $userId): void
    {
        try {
            $cacheKey = $this->getUserPermissionsCacheKey($companyId, $userId);
            Redis::del($cacheKey);
            
            Log::info('USER_PERMISSIONS_CACHE_INVALIDATED', [
                'company_id' => $companyId,
                'user_id' => $userId,
                'cache_key' => $cacheKey
            ]);

        } catch (\Exception $e) {
            Log::info('INVALIDATE_USER_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'user_id' => $userId
            ]);
        }
    }

    /**
     * 失效角色权限缓存（同时失效该角色下的所有用户缓存）
     * 
     * @param int $companyId 公司ID
     * @param int $roleId 角色ID
     */
    public function invalidateRolePermissions(int $companyId, int $roleId): void
    {
        try {
            // 失效角色权限缓存
            $roleCacheKey = $this->getRolePermissionsCacheKey($companyId, $roleId);
            Redis::del($roleCacheKey);

            // 获取该角色下的所有用户
            $userIds = CsCompanyAdmin::where('company_id', $companyId)
                ->where('role_id', $roleId)
                ->where('status', 1)
                ->pluck('id')
                ->toArray();

            // 批量失效用户权限缓存
            foreach ($userIds as $userId) {
                $this->invalidateUserPermissions($companyId, $userId);
            }

            Log::info('ROLE_PERMISSIONS_CACHE_INVALIDATED', [
                'company_id' => $companyId,
                'role_id' => $roleId,
                'affected_users' => count($userIds)
            ]);

        } catch (\Exception $e) {
            Log::info('INVALIDATE_ROLE_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'role_id' => $roleId
            ]);
        }
    }

    /**
     * 失效整个公司的权限缓存
     * 
     * @param int $companyId 公司ID
     */
    public function invalidateCompanyPermissions(int $companyId): void
    {
        try {
            // 使用Redis的SCAN命令批量删除
            $patterns = [
                self::USER_PERMISSIONS_PREFIX . ":{$companyId}:*",
                self::ROLE_PERMISSIONS_PREFIX . ":{$companyId}:*"
            ];

            foreach ($patterns as $pattern) {
                $this->deleteByPattern($pattern);
            }

            Log::info('COMPANY_PERMISSIONS_CACHE_INVALIDATED', [
                'company_id' => $companyId
            ]);

        } catch (\Exception $e) {
            Log::info('INVALIDATE_COMPANY_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId
            ]);
        }
    }

    /**
     * 失效菜单映射缓存
     * 
     * @param int|null $menuType 菜单类型（null表示全部）
     */
    public function invalidateMenuMapping(?int $menuType = null): void
    {
        try {
            if ($menuType !== null) {
                $cacheKey = $this->getMenuMappingCacheKey($menuType);
                Redis::del($cacheKey);
            } else {
                $this->deleteByPattern(self::MENU_MAPPING_PREFIX . ":*");
            }

            Log::info('MENU_MAPPING_CACHE_INVALIDATED', [
                'menu_type' => $menuType
            ]);

        } catch (\Exception $e) {
            Log::info('INVALIDATE_MENU_MAPPING_ERROR', [
                'error' => $e->getMessage(),
                'menu_type' => $menuType
            ]);
        }
    }

    /**
     * 权限缓存预热
     * 
     * @param int $companyId 公司ID
     */
    public function warmupPermissionCache(int $companyId): void
    {
        try {
            // 获取公司所有活跃用户
            $users = CsCompanyAdmin::where('company_id', $companyId)
                ->where('status', 1)
                ->select(['id', 'role_id'])
                ->get();

            $warmedUsers = 0;
            $warmedRoles = [];

            foreach ($users as $user) {
                // 预热用户权限缓存
                $this->getUserPermissions($companyId, $user->id);
                $warmedUsers++;

                // 预热角色权限缓存（避免重复）
                if (!in_array($user->role_id, $warmedRoles)) {
                    $this->getRolePermissions($companyId, $user->role_id);
                    $warmedRoles[] = $user->role_id;
                }
            }

            Log::info('PERMISSION_CACHE_WARMED_UP', [
                'company_id' => $companyId,
                'warmed_users' => $warmedUsers,
                'warmed_roles' => count($warmedRoles)
            ]);

        } catch (\Exception $e) {
            Log::info('WARMUP_PERMISSION_CACHE_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId
            ]);
        }
    }

    /**
     * 从数据库加载用户权限
     * 
     * @param int $companyId 公司ID
     * @param int $userId 用户ID
     * @return array 用户权限数据
     */
    private function loadUserPermissionsFromDB(int $companyId, int $userId): array
    {
        try {
            // 获取用户信息
            $admin = CsCompanyAdmin::where('id', $userId)
                ->where('company_id', $companyId)
                ->where('status', 1)
                ->first();

            if (!$admin) {
                return ['menu_ids' => [], 'menu_signs' => []];
            }

            // 获取角色权限
            $rolePermissions = $this->loadRolePermissionsFromDB($companyId, $admin->role_id);

            return $rolePermissions;

        } catch (\Exception $e) {
            Log::info('LOAD_USER_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'user_id' => $userId
            ]);

            return ['menu_ids' => [], 'menu_signs' => []];
        }
    }

    /**
     * 从数据库加载角色权限
     * 
     * @param int $companyId 公司ID
     * @param int $roleId 角色ID
     * @return array 角色权限数据
     */
    private function loadRolePermissionsFromDB(int $companyId, int $roleId): array
    {
        try {
            // 获取角色信息
            $role = CsCompanyAdminRole::where('id', $roleId)
                ->where(function($query) use ($companyId) {
                    $query->where('company_id', $companyId)
                          ->orWhere('id', 1); // 超级管理员角色
                })
                ->first();

            if (!$role) {
                return ['menu_ids' => [], 'menu_signs' => []];
            }

            // 处理权限字符串
            $rolePermission = $role->role_permission ?? '';
            
            if ($rolePermission === '*') {
                // 全权限：获取所有公司级菜单
                return $this->getAllCompanyMenuPermissions();
            }

            if (empty($rolePermission)) {
                return ['menu_ids' => [], 'menu_signs' => []];
            }

            // 解析权限ID列表
            $menuIds = array_filter(explode(',', $rolePermission));
            $menuIds = array_map('intval', $menuIds);

            if (empty($menuIds)) {
                return ['menu_ids' => [], 'menu_signs' => []];
            }

            // 获取菜单详情
            $menus = CsMenu::whereIn('id', $menuIds)
                ->where('status', 1)
                ->select(['id', 'menu_sign'])
                ->get();

            $menuSigns = $menus->pluck('menu_sign')->filter()->toArray();

            return [
                'menu_ids' => $menuIds,
                'menu_signs' => $menuSigns
            ];

        } catch (\Exception $e) {
            Log::info('LOAD_ROLE_PERMISSIONS_ERROR', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'role_id' => $roleId
            ]);

            return ['menu_ids' => [], 'menu_signs' => []];
        }
    }

    /**
     * 从数据库加载菜单映射
     * 
     * @param int $menuType 菜单类型
     * @return array 菜单映射数据
     */
    private function loadMenuMappingFromDB(int $menuType): array
    {
        try {
            $menus = CsMenu::where('menu_type', $menuType)
                ->where('status', 1)
                ->select(['id', 'menu_sign', 'menu_name'])
                ->get();

            $mapping = [];
            foreach ($menus as $menu) {
                if (!empty($menu->menu_sign)) {
                    $mapping[$menu->menu_sign] = [
                        'id' => $menu->id,
                        'name' => $menu->menu_name
                    ];
                }
            }

            return $mapping;

        } catch (\Exception $e) {
            Log::info('LOAD_MENU_MAPPING_ERROR', [
                'error' => $e->getMessage(),
                'menu_type' => $menuType
            ]);

            return [];
        }
    }

    /**
     * 获取所有公司级菜单权限
     * 
     * @return array 权限数据
     */
    private function getAllCompanyMenuPermissions(): array
    {
        try {
            $menus = CsMenu::where('menu_type', 1) // 公司级菜单
                ->where('status', 1)
                ->select(['id', 'menu_sign'])
                ->get();

            return [
                'menu_ids' => $menus->pluck('id')->toArray(),
                'menu_signs' => $menus->pluck('menu_sign')->filter()->toArray()
            ];

        } catch (\Exception $e) {
            Log::info('GET_ALL_COMPANY_MENUS_ERROR', [
                'error' => $e->getMessage()
            ]);

            return ['menu_ids' => [], 'menu_signs' => []];
        }
    }

    /**
     * 根据模式批量删除缓存
     * 
     * @param string $pattern Redis key模式
     */
    private function deleteByPattern(string $pattern): void
    {
        try {
            $keys = Redis::keys($pattern);
            if (!empty($keys)) {
                Redis::del(...$keys);
            }
        } catch (\Exception $e) {
            Log::info('DELETE_BY_PATTERN_ERROR', [
                'error' => $e->getMessage(),
                'pattern' => $pattern
            ]);
        }
    }

    /**
     * 生成用户权限缓存Key
     * 
     * @param int $companyId 公司ID
     * @param int $userId 用户ID
     * @return string 缓存Key
     */
    private function getUserPermissionsCacheKey(int $companyId, int $userId): string
    {
        return self::USER_PERMISSIONS_PREFIX . ":{$companyId}:{$userId}";
    }

    /**
     * 生成角色权限缓存Key
     * 
     * @param int $companyId 公司ID
     * @param int $roleId 角色ID
     * @return string 缓存Key
     */
    private function getRolePermissionsCacheKey(int $companyId, int $roleId): string
    {
        return self::ROLE_PERMISSIONS_PREFIX . ":{$companyId}:{$roleId}";
    }

    /**
     * 生成菜单映射缓存Key
     * 
     * @param int $menuType 菜单类型
     * @return string 缓存Key
     */
    private function getMenuMappingCacheKey(int $menuType): string
    {
        return self::MENU_MAPPING_PREFIX . ":{$menuType}";
    }
}