<template>
  <div class="test-container">
    <div class="test-header">
      <h1>套餐过期提醒功能测试</h1>
      <p>用于测试套餐过期提醒功能的各种场景</p>
    </div>

    <div class="test-section">
      <h3>当前用户信息</h3>
      <div class="info-card" v-if="authStore.user">
        <div class="info-item">
          <span class="label">用户名：</span>
          <span class="value">{{ authStore.user.username }}</span>
        </div>
        <div class="info-item">
          <span class="label">企业名称：</span>
          <span class="value">{{ authStore.user.company_info?.company_name }}</span>
        </div>
        <div class="info-item">
          <span class="label">套餐版本：</span>
          <span class="value">{{ authStore.user.version_info?.version_name }}</span>
        </div>
        <div class="info-item">
          <span class="label">到期时间：</span>
          <span class="value">{{ authStore.user.company_info?.expired_at }}</span>
        </div>
        <div class="info-item">
          <span class="label">剩余天数：</span>
          <span class="value" :class="getRemainingDaysClass()">
            {{ authStore.user.company_info?.remaining_days }} 天
          </span>
        </div>
        <div class="info-item">
          <span class="label">是否已过期：</span>
          <span class="value" :class="{ 'expired': authStore.user.company_info?.is_expired }">
            {{ authStore.user.company_info?.is_expired ? '是' : '否' }}
          </span>
        </div>
        <div class="info-item">
          <span class="label">显示过期提醒：</span>
          <span class="value" :class="{ 'warning': authStore.user.company_info?.show_expiration_reminder }">
            {{ authStore.user.company_info?.show_expiration_reminder ? '是' : '否' }}
          </span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试操作</h3>
      <div class="button-group">
        <button class="test-btn primary" @click="showTestReminder">
          显示测试提醒弹窗
        </button>
        <button class="test-btn" @click="resetReminderState">
          重置提醒状态
        </button>
        <button class="test-btn" @click="refreshUserInfo">
          刷新用户信息
        </button>
        <button class="test-btn" @click="checkReminderLogic">
          检查提醒逻辑
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>提醒状态信息</h3>
      <div class="status-card">
        <div class="status-item">
          <span class="label">今日提醒日期：</span>
          <span class="value">{{ lastReminderDate || '未设置' }}</span>
        </div>
        <div class="status-item">
          <span class="label">当前日期：</span>
          <span class="value">{{ currentDate }}</span>
        </div>
        <div class="status-item">
          <span class="label">是否应该显示提醒：</span>
          <span class="value" :class="{ 'warning': shouldShowReminder }">
            {{ shouldShowReminder ? '是' : '否' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 测试用的提醒弹窗 -->
    <ExpirationReminderDialog
      v-model="showTestDialog"
      :remaining-days="testRemainingDays"
      :expired-at="testExpiredAt"
      :version-name="testVersionName"
      @contact-service="handleTestContactService"
      @remind-later="handleTestRemindLater"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useExpirationReminder } from '@/composables/useExpirationReminder'
import ExpirationReminderDialog from '@/components/ExpirationReminderDialog.vue'

const authStore = useAuthStore()
const { resetReminderState, checkExpirationReminder } = useExpirationReminder()

const showTestDialog = ref(false)
const lastReminderDate = ref('')
const currentDate = ref('')

// 测试数据
const testRemainingDays = ref(15)
const testExpiredAt = ref('2024-09-15')
const testVersionName = ref('专业版')

const shouldShowReminder = computed(() => {
  if (!authStore.user?.company_info) return false
  
  const { is_expired, show_expiration_reminder } = authStore.user.company_info
  const today = new Date().toDateString()
  const lastReminder = localStorage.getItem('expiration_reminder_date')
  
  return !is_expired && show_expiration_reminder && lastReminder !== today
})

const getRemainingDaysClass = () => {
  const days = authStore.user?.company_info?.remaining_days || 0
  if (days <= 3) return 'urgent'
  if (days <= 7) return 'warning'
  if (days <= 30) return 'notice'
  return ''
}

const showTestReminder = () => {
  showTestDialog.value = true
}

const resetReminderState = () => {
  resetReminderState()
  updateReminderStatus()
  ElMessage.success('提醒状态已重置')
}

const refreshUserInfo = async () => {
  try {
    await authStore.initUserInfo()
    updateReminderStatus()
    ElMessage.success('用户信息已刷新')
  } catch (error) {
    ElMessage.error('刷新用户信息失败')
  }
}

const checkReminderLogic = () => {
  if (authStore.user) {
    const result = checkExpirationReminder(authStore.user as any)
    ElMessage.info(`提醒逻辑检查结果：${result ? '应该显示提醒' : '不需要显示提醒'}`)
  }
}

const handleTestContactService = () => {
  ElMessage.success('测试：联系客服功能')
}

const handleTestRemindLater = () => {
  ElMessage.info('测试：稍后提醒功能')
  updateReminderStatus()
}

const updateReminderStatus = () => {
  lastReminderDate.value = localStorage.getItem('expiration_reminder_date') || ''
  currentDate.value = new Date().toDateString()
}

onMounted(() => {
  updateReminderStatus()
})
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.test-header p {
  color: #909399;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 10px;
}

.info-card, .status-card {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 15px;
}

.info-item, .status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.info-item:last-child, .status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
}

.value.urgent {
  color: #f56c6c;
  font-weight: 600;
}

.value.warning {
  color: #e6a23c;
  font-weight: 600;
}

.value.notice {
  color: #409eff;
}

.value.expired {
  color: #f56c6c;
  font-weight: 600;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: white;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.test-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.test-btn.primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.test-btn.primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}
</style>
