/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AdminFormDialog: typeof import('./components/AdminFormDialog.vue')['default']
    ApiExample: typeof import('./components/ApiExample.vue')['default']
    ConfirmDialog: typeof import('./components/ConfirmDialog.vue')['default']
    CustomerServiceDialog: typeof import('./components/CustomerServiceDialog.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElTag: typeof import('element-plus/es')['ElTag']
    ExpirationReminderDialog: typeof import('./components/ExpirationReminderDialog.vue')['default']
    Header: typeof import('./components/Header.vue')['default']
    HtmlContentDialog: typeof import('./components/HtmlContentDialog.vue')['default']
    MessageToast: typeof import('./components/MessageToast.vue')['default']
    ProfileDialog: typeof import('./components/ProfileDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShopFormDialog: typeof import('./components/ShopFormDialog.vue')['default']
    Sidebar: typeof import('./components/Sidebar.vue')['default']
    VersionInfoDialog: typeof import('./components/VersionInfoDialog.vue')['default']
  }
}
