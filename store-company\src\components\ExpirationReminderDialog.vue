<template>
  <el-dialog
    v-model="visible"
    title="套餐即将到期提醒"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    center
    class="expiration-reminder-dialog"
  >
    <div class="reminder-content">
      <div class="reminder-icon">
        <el-icon :size="60" color="#f56c6c">
          <Warning />
        </el-icon>
      </div>
      
      <div class="reminder-message">
        <h3 class="reminder-title">套餐即将到期</h3>
        <p class="reminder-text">
          您的套餐将在 <span class="highlight">{{ remainingDays }}</span> 天后到期
        </p>
        <p class="reminder-description">
          为避免影响正常使用，请及时联系客服续费套餐
        </p>
      </div>
      
      <div class="package-info">
        <div class="info-item">
          <span class="info-label">当前套餐：</span>
          <span class="info-value">{{ versionName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">到期时间：</span>
          <span class="info-value">{{ expiredAt }}</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleRemindLater">稍后提醒</el-button>
        <el-button type="primary" @click="handleContactService">
          联系客服续费
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  remainingDays: number
  expiredAt: string
  versionName: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'contact-service': []
  'remind-later': []
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleContactService = () => {
  emit('contact-service')
  visible.value = false
}

const handleRemindLater = () => {
  emit('remind-later')
  visible.value = false
  ElMessage.info('将在明天再次提醒您')
}
</script>

<style scoped>
.expiration-reminder-dialog {
  --el-dialog-border-radius: 12px;
}

.reminder-content {
  text-align: center;
  padding: 20px 0;
}

.reminder-icon {
  margin-bottom: 20px;
}

.reminder-message {
  margin-bottom: 30px;
}

.reminder-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px 0;
}

.reminder-text {
  font-size: 16px;
  color: #606266;
  margin: 0 0 10px 0;
}

.highlight {
  color: #f56c6c;
  font-weight: 600;
  font-size: 18px;
}

.reminder-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.package-info {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #909399;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-dialog__header) {
  text-align: center;
  padding: 20px 20px 0 20px;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 0 20px 20px 20px;
}
</style>
