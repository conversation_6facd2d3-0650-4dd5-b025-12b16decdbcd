<?php

namespace app\service;

use support\Log;

/**
 * 路由权限映射服务
 * 
 * 提供控制器方法到权限标识的自动映射功能
 * 支持自定义映射规则和缓存优化
 */
class RoutePermissionService
{
    /**
     * 控制器方法到权限标识的映射规则
     * 
     * 格式：'ControllerName::method' => 'menu_sign'
     */
    private array $routePermissionMap = [
        // 会员管理模块
        'CsUserController::add' => 'member_add',
        'CsUserController::addPost' => 'member_add',
        'CsUserController::edit' => 'member_edit',
        'CsUserController::editPost' => 'member_edit',
        'CsUserController::del' => 'member_delete',
        'CsUserController::selectDel' => 'member_delete',
        'CsUserController::state' => 'member_status',
        'CsUserController::recharge' => 'member_recharge',

        // 会员等级管理
        'CsUserLevelController::add' => 'member_level_add',
        'CsUserLevelController::addPost' => 'member_level_add',
        'CsUserLevelController::edit' => 'member_level_edit',
        'CsUserLevelController::editPost' => 'member_level_edit',
        'CsUserLevelController::del' => 'member_level_delete',
        'CsUserLevelController::selectDel' => 'member_level_delete',
        'CsUserLevelController::sort' => 'member_level_sort',

        // 会员充值套餐管理
        'CsUserRechargePackageController::add' => 'member_recharge_add',
        'CsUserRechargePackageController::addPost' => 'member_recharge_add',
        'CsUserRechargePackageController::edit' => 'member_recharge_edit',
        'CsUserRechargePackageController::editPost' => 'member_recharge_edit',
        'CsUserRechargePackageController::del' => 'member_recharge_delete',
        'CsUserRechargePackageController::selectDel' => 'member_recharge_delete',

        // 会员余额明细
        'CsUserBalanceLogController::detail' => 'member_balance_detail',

        // 会员积分明细
        'CsUserPointsLogController::detail' => 'member_points_detail',

        // 订单管理模块
        'CsOrderController::detail' => 'order_table_detail',
        'CsRetailController::detail' => 'order_retail_detail',
        'CsRetailController::cancel' => 'order_retail_cancel',

        // 优惠券管理
        'CsCouponController::add' => 'marketing_coupon_add',
        'CsCouponController::addPost' => 'marketing_coupon_add',
        'CsCouponController::edit' => 'marketing_coupon_edit',
        'CsCouponController::editPost' => 'marketing_coupon_edit',
        'CsCouponController::del' => 'marketing_coupon_delete',
        'CsCouponController::selectDel' => 'marketing_coupon_delete',
        'CsCouponController::state' => 'marketing_coupon_status',

        // 营销活动管理
        'CsMarketingActivityController::add' => 'marketing_activity_add',
        'CsMarketingActivityController::addPost' => 'marketing_activity_add',
        'CsMarketingActivityController::edit' => 'marketing_activity_edit',
        'CsMarketingActivityController::editPost' => 'marketing_activity_edit',
        'CsMarketingActivityController::del' => 'marketing_activity_delete',
        'CsMarketingActivityController::selectDel' => 'marketing_activity_delete',
        'CsMarketingActivityController::state' => 'marketing_activity_status',

        // 库存管理
        'CsInventoryController::adjust' => 'inventory_adjust',
        'CsInventoryController::record' => 'inventory_record',
        'CsInventoryController::export' => 'inventory_export',

        // 库存盘点
        'CsInventoryCheckController::create' => 'inventory_check_create',
        'CsInventoryCheckController::start' => 'inventory_check_start',
        'CsInventoryCheckController::complete' => 'inventory_check_complete',
        'CsInventoryCheckController::cancel' => 'inventory_check_cancel',
        'CsInventoryCheckController::del' => 'inventory_check_delete',
        'CsInventoryCheckController::detail' => 'inventory_check_detail',
        'CsInventoryCheckController::execute' => 'inventory_check_execute',
        'CsInventoryCheckController::export' => 'inventory_check_export',

        // 库存变动记录
        'CsInventoryLogController::detail' => 'inventory_log_detail',
        'CsInventoryLogController::export' => 'inventory_log_export',
        'CsInventoryLogController::statistics' => 'inventory_log_statistics',

        // 消息管理
        'CsRealtimeMessageController::detail' => 'message_center_detail',
        'CsRealtimeMessageController::read' => 'message_center_read',

        // 桌台分类管理
        'CsTableClassificationController::add' => 'table_category_add',
        'CsTableClassificationController::addPost' => 'table_category_add',
        'CsTableClassificationController::edit' => 'table_category_edit',
        'CsTableClassificationController::editPost' => 'table_category_edit',
        'CsTableClassificationController::del' => 'table_category_delete',
        'CsTableClassificationController::selectDel' => 'table_category_delete',
        'CsTableClassificationController::state' => 'table_category_status',
        'CsTableClassificationController::sort' => 'table_category_sort',

        // 桌台管理
        'CsTableController::add' => 'table_add',
        'CsTableController::addPost' => 'table_add',
        'CsTableController::edit' => 'table_edit',
        'CsTableController::editPost' => 'table_edit',
        'CsTableController::del' => 'table_delete',
        'CsTableController::selectDel' => 'table_delete',
        'CsTableController::state' => 'table_status',
        'CsTableController::sort' => 'table_sort',

        // 商品分类管理
        'CsGoodsCategoryController::add' => 'goods_category_add',
        'CsGoodsCategoryController::addPost' => 'goods_category_add',
        'CsGoodsCategoryController::edit' => 'goods_category_edit',
        'CsGoodsCategoryController::editPost' => 'goods_category_edit',
        'CsGoodsCategoryController::del' => 'goods_category_delete',
        'CsGoodsCategoryController::selectDel' => 'goods_category_delete',
        'CsGoodsCategoryController::state' => 'goods_category_status',
        'CsGoodsCategoryController::sort' => 'goods_category_sort',

        // 商品管理
        'CsGoodsController::add' => 'goods_add',
        'CsGoodsController::addPost' => 'goods_add',
        'CsGoodsController::edit' => 'goods_edit',
        'CsGoodsController::editPost' => 'goods_edit',
        'CsGoodsController::del' => 'goods_delete',
        'CsGoodsController::selectDel' => 'goods_delete',
        'CsGoodsController::state' => 'goods_status',
        'CsGoodsController::sort' => 'goods_sort',

        // 套餐管理
        'CsSetMealGoodsController::add' => 'goods_package_add',
        'CsSetMealGoodsController::addPost' => 'goods_package_add',
        'CsSetMealGoodsController::edit' => 'goods_package_edit',
        'CsSetMealGoodsController::editPost' => 'goods_package_edit',
        'CsSetMealGoodsController::del' => 'goods_package_delete',
        'CsSetMealGoodsController::selectDel' => 'goods_package_delete',
        'CsSetMealGoodsController::state' => 'goods_package_status',
        'CsSetMealGoodsController::sort' => 'goods_package_sort',

        // 配品分类管理
        'CsAccessoriesGoodsCategoryController::add' => 'goods_material_category_add',
        'CsAccessoriesGoodsCategoryController::addPost' => 'goods_material_category_add',
        'CsAccessoriesGoodsCategoryController::edit' => 'goods_material_category_edit',
        'CsAccessoriesGoodsCategoryController::editPost' => 'goods_material_category_edit',
        'CsAccessoriesGoodsCategoryController::del' => 'goods_material_category_delete',
        'CsAccessoriesGoodsCategoryController::selectDel' => 'goods_material_category_delete',
        'CsAccessoriesGoodsCategoryController::state' => 'goods_material_category_status',
        'CsAccessoriesGoodsCategoryController::sort' => 'goods_material_category_sort',

        // 配品管理
        'CsAccessoriesGoodsController::add' => 'goods_material_add',
        'CsAccessoriesGoodsController::addPost' => 'goods_material_add',
        'CsAccessoriesGoodsController::edit' => 'goods_material_edit',
        'CsAccessoriesGoodsController::editPost' => 'goods_material_edit',
        'CsAccessoriesGoodsController::del' => 'goods_material_delete',
        'CsAccessoriesGoodsController::selectDel' => 'goods_material_delete',
        'CsAccessoriesGoodsController::state' => 'goods_material_status',
        'CsAccessoriesGoodsController::sort' => 'goods_material_sort',

        // 标签管理
        'CsGoodsRemarkLabelController::add' => 'goods_tag_add',
        'CsGoodsRemarkLabelController::addPost' => 'goods_tag_add',
        'CsGoodsRemarkLabelController::edit' => 'goods_tag_edit',
        'CsGoodsRemarkLabelController::editPost' => 'goods_tag_edit',
        'CsGoodsRemarkLabelController::del' => 'goods_tag_delete',
        'CsGoodsRemarkLabelController::selectDel' => 'goods_tag_delete',
        'CsGoodsRemarkLabelController::state' => 'goods_tag_status',
        'CsGoodsRemarkLabelController::sort' => 'goods_tag_sort',

        // 规格分类管理
        'CsSpecificationClassificationController::add' => 'goods_spec_category_add',
        'CsSpecificationClassificationController::addPost' => 'goods_spec_category_add',
        'CsSpecificationClassificationController::edit' => 'goods_spec_category_edit',
        'CsSpecificationClassificationController::editPost' => 'goods_spec_category_edit',
        'CsSpecificationClassificationController::del' => 'goods_spec_category_delete',
        'CsSpecificationClassificationController::selectDel' => 'goods_spec_category_delete',
        'CsSpecificationClassificationController::state' => 'goods_spec_category_status',
        'CsSpecificationClassificationController::sort' => 'goods_spec_category_sort',

        // 规格管理
        'CsSpecificationController::add' => 'goods_spec_add',
        'CsSpecificationController::addPost' => 'goods_spec_add',
        'CsSpecificationController::edit' => 'goods_spec_edit',
        'CsSpecificationController::editPost' => 'goods_spec_edit',
        'CsSpecificationController::del' => 'goods_spec_delete',
        'CsSpecificationController::selectDel' => 'goods_spec_delete',
        'CsSpecificationController::state' => 'goods_spec_status',
        'CsSpecificationController::sort' => 'goods_spec_sort',

        // 收款方式管理
        'CsPaymentMethodController::add' => 'payment_method_add',
        'CsPaymentMethodController::addPost' => 'payment_method_add',
        'CsPaymentMethodController::edit' => 'payment_method_edit',
        'CsPaymentMethodController::editPost' => 'payment_method_edit',
        'CsPaymentMethodController::del' => 'payment_method_delete',
        'CsPaymentMethodController::selectDel' => 'payment_method_delete',
        'CsPaymentMethodController::state' => 'payment_method_status',
        'CsPaymentMethodController::sort' => 'payment_method_sort',

        // 桌台费规则管理
        'CsBillingRuleController::add' => 'table_billing_add',
        'CsBillingRuleController::addPost' => 'table_billing_add',
        'CsBillingRuleController::edit' => 'table_billing_edit',
        'CsBillingRuleController::editPost' => 'table_billing_edit',
        'CsBillingRuleController::del' => 'table_billing_delete',
        'CsBillingRuleController::selectDel' => 'table_billing_delete',
        'CsBillingRuleController::state' => 'table_billing_status',
        'CsBillingRuleController::sort' => 'table_billing_sort',

        // 服务费规则管理
        'CsServiceRuleController::add' => 'service_fee_add',
        'CsServiceRuleController::addPost' => 'service_fee_add',
        'CsServiceRuleController::edit' => 'service_fee_edit',
        'CsServiceRuleController::editPost' => 'service_fee_edit',
        'CsServiceRuleController::del' => 'service_fee_delete',
        'CsServiceRuleController::selectDel' => 'service_fee_delete',
        'CsServiceRuleController::state' => 'service_fee_status',
        'CsServiceRuleController::sort' => 'service_fee_sort',

        // 打印机模板设置
        'CsPrintTemplateController::edit' => 'print_template_edit',
        'CsPrintTemplateController::editPost' => 'print_template_edit',
        'CsPrintTemplateController::preview' => 'print_template_preview',
        'CsPrintTemplateController::state' => 'print_template_status',

        // 打印机设置
        'CsPrintConfigController::add' => 'printer_config_add',
        'CsPrintConfigController::addPost' => 'printer_config_add',
        'CsPrintConfigController::edit' => 'printer_config_edit',
        'CsPrintConfigController::editPost' => 'printer_config_edit',
        'CsPrintConfigController::del' => 'printer_config_delete',
        'CsPrintConfigController::selectDel' => 'printer_config_delete',
        'CsPrintConfigController::testConnect' => 'printer_config_test_connect',
        'CsPrintConfigController::testPrint' => 'printer_config_test_print',
        'CsPrintConfigController::copyConfig' => 'printer_config_copy_config',
        'CsPrintConfigController::state' => 'printer_config_status',

        // 管理员管理
        'CsShopAdminController::add' => 'admin_add',
        'CsShopAdminController::addPost' => 'admin_add',
        'CsShopAdminController::edit' => 'admin_edit',
        'CsShopAdminController::editPost' => 'admin_edit',
        'CsShopAdminController::del' => 'admin_delete',
        'CsShopAdminController::selectDel' => 'admin_delete',
        'CsShopAdminController::state' => 'admin_status',

        // 角色管理
        'CsShopAdminRoleController::add' => 'role_add',
        'CsShopAdminRoleController::addPost' => 'role_add',
        'CsShopAdminRoleController::edit' => 'role_edit',
        'CsShopAdminRoleController::editPost' => 'role_edit',
        'CsShopAdminRoleController::del' => 'role_delete',
        'CsShopAdminRoleController::selectDel' => 'role_delete',
        'CsShopAdminRoleController::permission' => 'role_permission_assign',

        // 预订管理
        'CsReservationController::detail' => 'reservation_detail',
        'CsReservationController::open' => 'reservation_open',
        'CsReservationController::changeTable' => 'reservation_change_table',
        'CsReservationController::edit' => 'reservation_edit',
        'CsReservationController::editPost' => 'reservation_edit',
        'CsReservationController::cancel' => 'reservation_cancel',
    ];

    /**
     * 不需要权限检查的路由
     * 
     * 包括登录、注册等公共接口
     */
    private array $publicRoutes = [
        'LoginController::login',
        'LoginController::logout',
        'LoginController::register',
        'LoginController::getLoginPage',
        'LoginController::savePwd',
        'LoginController::getCustomerServiceInfo',
        'LoginController::sendForgotPasswordSms',
        'LoginController::verifyForgotPasswordSms',
        'LoginController::resetPassword',
        'LoginController::resendForgotPasswordSms',
        'LoginController::sendRegisterSms',
        'LoginController::getUserAgreement',
        'LoginController::getPrivacyPolicy',
        'IndexController::index',
        'SmsController::send',
        'UploadController::image',
        'UploadController::file',
    ];

    /**
     * 根据控制器和方法获取权限标识
     * 
     * @param string $controller 控制器名称
     * @param string $action 方法名称
     * @return string|null 权限标识，null表示不需要权限检查
     */
    public function getMenuSignByRoute(string $controller, string $action): ?string
    {
        try {
            // 清理控制器名称（移除命名空间）
            $controllerName = $this->getCleanControllerName($controller);
            $routeKey = "{$controllerName}::{$action}";

            // 检查是否为公共路由
            if (in_array($routeKey, $this->publicRoutes)) {
                return null;
            }

            // 查找权限映射
            if (isset($this->routePermissionMap[$routeKey])) {
                return $this->routePermissionMap[$routeKey];
            }

            // 尝试自动映射：基于命名约定
            $autoMappedSign = $this->autoMapRouteToPermission($controllerName, $action);
            if ($autoMappedSign) {
                return $autoMappedSign;
            }

            // 未找到映射，记录日志
            Log::info('ROUTE_PERMISSION_MAPPING_NOT_FOUND', [
                'controller' => $controllerName,
                'action' => $action,
                'route_key' => $routeKey
            ]);

            // 默认需要权限检查，但权限标识为空会导致拒绝访问
            return 'unknown_permission';

        } catch (\Exception $e) {
            Log::info('GET_MENU_SIGN_BY_ROUTE_ERROR', [
                'error' => $e->getMessage(),
                'controller' => $controller,
                'action' => $action
            ]);

            return 'unknown_permission';
        }
    }

    /**
     * 添加路由权限映射
     * 
     * @param string $controller 控制器名称
     * @param string $action 方法名称
     * @param string $menuSign 权限标识
     */
    public function addRoutePermissionMapping(string $controller, string $action, string $menuSign): void
    {
        $controllerName = $this->getCleanControllerName($controller);
        $routeKey = "{$controllerName}::{$action}";
        
        $this->routePermissionMap[$routeKey] = $menuSign;
        
        Log::info('ROUTE_PERMISSION_MAPPING_ADDED', [
            'route_key' => $routeKey,
            'menu_sign' => $menuSign
        ]);
    }

    /**
     * 添加公共路由（不需要权限检查）
     * 
     * @param string $controller 控制器名称
     * @param string $action 方法名称
     */
    public function addPublicRoute(string $controller, string $action): void
    {
        $controllerName = $this->getCleanControllerName($controller);
        $routeKey = "{$controllerName}::{$action}";
        
        $this->publicRoutes[] = $routeKey;
        
        Log::info('PUBLIC_ROUTE_ADDED', [
            'route_key' => $routeKey
        ]);
    }

    /**
     * 获取所有路由权限映射
     * 
     * @return array 映射数组
     */
    public function getAllRoutePermissionMappings(): array
    {
        return $this->routePermissionMap;
    }

    /**
     * 获取所有公共路由
     * 
     * @return array 公共路由数组
     */
    public function getAllPublicRoutes(): array
    {
        return $this->publicRoutes;
    }

    /**
     * 清理控制器名称
     * 
     * 移除命名空间，只保留类名
     * 
     * @param string $controller 完整控制器名称
     * @return string 清理后的控制器名称
     */
    private function getCleanControllerName(string $controller): string
    {
        // 移除命名空间
        $parts = explode('\\', $controller);
        return end($parts);
    }

    /**
     * 基于命名约定自动映射路由到权限
     * 
     * @param string $controllerName 控制器名称
     * @param string $action 方法名称
     * @return string|null 自动映射的权限标识
     */
    private function autoMapRouteToPermission(string $controllerName, string $action): ?string
    {
        try {
            // 通用的控制器名称到模块名称的映射
            $controllerModuleMap = [
                'CsUserController' => 'member',
                'CsUserLevelController' => 'member_level',
                'CsUserRechargePackageController' => 'member_recharge',
                'CsUserBalanceLogController' => 'member_balance',
                'CsUserPointsLogController' => 'member_points',
                'CsOrderController' => 'order_table',
                'CsRetailController' => 'order_retail',
                'CsCouponController' => 'marketing_coupon',
                'CsMarketingActivityController' => 'marketing_activity',
                'CsInventoryController' => 'inventory',
                'CsInventoryCheckController' => 'inventory_check',
                'CsInventoryLogController' => 'inventory_log',
                'CsRealtimeMessageController' => 'message_center',
                'CsTableClassificationController' => 'table_category',
                'CsTableController' => 'table',
                'CsGoodsCategoryController' => 'goods_category',
                'CsGoodsController' => 'goods',
                'CsSetMealGoodsController' => 'goods_package',
                'CsAccessoriesGoodsCategoryController' => 'goods_material_category',
                'CsAccessoriesGoodsController' => 'goods_material',
                'CsGoodsRemarkLabelController' => 'goods_tag',
                'CsSpecificationClassificationController' => 'goods_spec_category',
                'CsSpecificationController' => 'goods_spec',
                'CsPaymentMethodController' => 'payment_method',
                'CsBillingRuleController' => 'table_billing',
                'CsServiceRuleController' => 'service_fee',
                'CsPrintTemplateController' => 'print_template',
                'CsPrintConfigController' => 'printer_config',
                'CsShopAdminController' => 'admin',
                'CsShopAdminRoleController' => 'role',
                'CsReservationController' => 'reservation',
            ];

            // 通用的方法名称到操作类型的映射
            $actionPermissionMap = [
                'add' => 'add',
                'addPost' => 'add',
                'edit' => 'edit',
                'editPost' => 'edit',
                'del' => 'delete',
                'selectDel' => 'delete',
                'state' => 'status',
                'sort' => 'sort',
                'detail' => 'detail',
                'export' => 'export',
                'import' => 'import',
                'preview' => 'preview',
                'copy' => 'copy',
                'create' => 'create',
                'start' => 'start',
                'complete' => 'complete',
                'cancel' => 'cancel',
                'execute' => 'execute',
                'statistics' => 'statistics',
                'recharge' => 'recharge',
                'read' => 'read',
                'testConnect' => 'test_connect',
                'testPrint' => 'test_print',
                'copyConfig' => 'copy_config',
                'changeTable' => 'change_table',
                'open' => 'open',
                'permission' => 'permission_assign',
            ];

            // 查找模块映射
            if (!isset($controllerModuleMap[$controllerName])) {
                return null;
            }

            $modulePrefix = $controllerModuleMap[$controllerName];

            // 查找操作映射
            if (!isset($actionPermissionMap[$action])) {
                return null;
            }

            $actionSuffix = $actionPermissionMap[$action];

            // 组合权限标识
            return "{$modulePrefix}_{$actionSuffix}";

        } catch (\Exception $e) {
            Log::info('AUTO_MAP_ROUTE_PERMISSION_ERROR', [
                'error' => $e->getMessage(),
                'controller' => $controllerName,
                'action' => $action
            ]);

            return null;
        }
    }

    /**
     * 验证权限标识是否存在
     * 
     * @param string $menuSign 权限标识
     * @return bool 是否存在
     */
    public function validateMenuSign(string $menuSign): bool
    {
        try {
            // 这里可以添加对数据库的查询验证
            // 目前简单检查格式
            return !empty($menuSign) && 
                   strlen($menuSign) > 3 && 
                   preg_match('/^[a-z_]+$/', $menuSign);

        } catch (\Exception $e) {
            Log::info('VALIDATE_MENU_SIGN_ERROR', [
                'error' => $e->getMessage(),
                'menu_sign' => $menuSign
            ]);

            return false;
        }
    }

    /**
     * 获取权限映射统计信息
     * 
     * @return array 统计信息
     */
    public function getPermissionMappingStats(): array
    {
        return [
            'total_mappings' => count($this->routePermissionMap),
            'public_routes' => count($this->publicRoutes),
            'last_updated' => date('Y-m-d H:i:s')
        ];
    }
}