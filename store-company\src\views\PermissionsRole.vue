<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">角色管理</h1>
      <p class="page-description">管理系统角色信息，设置角色权限</p>
    </div>

    <div class="card">
      <div class="card-header">
        <div class="card-actions">
          <button
            v-if="hasPermission('role_add')"
            class="btn btn-primary"
            @click="showAddRoleModal = true">
            <i class="icon">➕</i> 新增角色
          </button>
        </div>
      </div>
      <div class="card-body">
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th width="80">序号</th>
                <th width="200">角色名称</th>
                <th width="200">添加时间</th>
                <th width="200">更新时间</th>
                <th width="300">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(role, ind) in roleList" :key="role.id">
                <td>{{ ind + 1 }}</td>
                <td>{{ role.role_name }}</td>
                <td>{{ formatDate(role.created_at) }}</td>
                <td>{{ formatDate(role.updated_at) }}</td>
                <td>
                  <div class="action-buttons">
                    <button
                      v-if="hasPermission('role_permission_assign')"
                      class="btn btn-sm btn-warning"
                      @click="managePermissions(role)"
                      title="权限管理">
                      <i class="icon">🔐</i> 权限
                    </button>
                    <button
                      v-if="hasPermission('role_edit')"
                      class="btn btn-sm btn-primary"
                      @click="editRole(role)"
                      title="编辑角色">
                      <i class="icon">✏️</i> 编辑
                    </button>
                    <button
                      v-if="hasPermission('role_delete')"
                      class="btn btn-sm btn-danger"
                      @click="deleteRole(role)"
                      title="删除角色">
                      <i class="icon">🗑️</i> 删除
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 角色新增模态框 -->
    <div v-if="showAddRoleModal" class="modal-overlay" @click.self="showAddRoleModal = false">
      <div class="modal-dialog">
        <div class="modal-header">
          <h3 class="modal-title">新增角色</h3>
          <button class="modal-close" @click="showAddRoleModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="role-form">
            <div class="form-group">
              <label class="form-label">角色名称 <span class="required">*</span></label>
              <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddRoleModal = false">取消</button>
          <button class="btn btn-primary" @click="saveRole">
            <i class="icon">💾</i> 保存角色
          </button>
        </div>
      </div>
    </div>

    <!-- 角色编辑模态框 -->
    <div v-if="showEditRoleModal" class="modal-overlay" @click.self="showEditRoleModal = false">
      <div class="modal-dialog">
        <div class="modal-header">
          <h3 class="modal-title">编辑角色</h3>
          <button class="modal-close" @click="showEditRoleModal = false">×</button>
        </div>
        <div class="modal-body">
          <form class="role-form">
            <div class="form-group">
              <label class="form-label">角色名称 <span class="required">*</span></label>
              <input type="text" class="form-control" v-model="roleForm.role_name" placeholder="请输入角色名称">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditRoleModal = false">取消</button>
          <button class="btn btn-primary" @click="saveRole">
            <i class="icon">💾</i> 保存修改
          </button>
        </div>
      </div>
    </div>

    <!-- 权限管理模态框 -->
    <div v-if="showPermissionsModal" class="modal-overlay" @click.self="showPermissionsModal = false">
      <div class="modal-dialog modal-lg">
        <div class="modal-header">
          <h3 class="modal-title">权限管理 - {{ currentRole?.role_name }}</h3>
          <button class="modal-close" @click="showPermissionsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="permissions-container">
            <div class="permission-tree" v-for="menu in permissionMenu" :key="menu.id">
              <div class="tree-node">
                <div class="node-content parent-node">
                  <span class="expand-icon" @click="toggleExpand(menu.id)" :class="{ expanded: expandedNodes.includes(menu.id) }">
                    {{ expandedNodes.includes(menu.id) ? '▼' : '▶' }}
                  </span>
                  <label class="checkbox-label">
                    <input type="checkbox"
                           :ref="el => setCheckboxRef(el, menu.id)"
                           :checked="isMenuAllSelected(menu)"
                           @change="toggleMenuPermissions(menu, $event.target.checked)">
                    <strong>{{ menu.menu_name }}</strong>
                  </label>
                </div>

                <!-- 子菜单 -->
                <div v-if="expandedNodes.includes(menu.id) && menu.cs_menu_recursive && menu.cs_menu_recursive.length > 0"
                     class="children-nodes">
                  <!-- 有子菜单的子菜单项 -->
                  <div v-for="child in getSubMenusWithChildren(menu.cs_menu_recursive)" :key="child.id" class="tree-node child-node">
                    <div class="node-content">
                      <span class="expand-icon"
                            @click="toggleExpand(child.id)"
                            :class="{ expanded: expandedNodes.includes(child.id) }">
                        {{ expandedNodes.includes(child.id) ? '▼' : '▶' }}
                      </span>
                      <label class="checkbox-label">
                        <input type="checkbox"
                               :ref="el => setCheckboxRef(el, child.id)"
                               :checked="isMenuAllSelected(child)"
                               @change="toggleMenuPermissions(child, $event.target.checked)">
                        <strong>{{ child.menu_name }}</strong>
                      </label>
                    </div>

                    <!-- 权限项 -->
                    <div v-if="expandedNodes.includes(child.id) && child.cs_menu_recursive && child.cs_menu_recursive.length > 0"
                         class="permission-items">
                      <label class="checkbox-label permission-item"
                             v-for="permission in child.cs_menu_recursive"
                             :key="permission.id">
                        <input type="checkbox"
                               v-model="selectedPermissions"
                               :value="permission.id.toString()">
                        {{ permission.menu_name }}
                      </label>
                    </div>
                  </div>

                  <!-- 没有子菜单的权限项（横向排列） -->
                  <div v-if="getLeafPermissions(menu.cs_menu_recursive).length > 0" class="permission-items">
                    <label class="checkbox-label permission-item"
                           v-for="permission in getLeafPermissions(menu.cs_menu_recursive)"
                           :key="permission.id">
                      <input type="checkbox"
                             v-model="selectedPermissions"
                             :value="permission.id.toString()">
                      {{ permission.menu_name }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showPermissionsModal = false">取消</button>
          <button class="btn btn-primary" @click="savePermissions">
            <i class="icon">💾</i> 保存权限
          </button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      :visible="confirmDialogState.visible"
      :type="confirmDialogState.type"
      :title="confirmDialogState.title"
      :message="confirmDialogState.message"
      :details="confirmDialogState.details"
      :confirm-text="confirmDialogState.confirmText"
      :cancel-text="confirmDialogState.cancelText"
      :loading="confirmDialogState.loading"
      :loading-text="confirmDialogState.loadingText"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <!-- 消息提示 -->
    <MessageToast ref="messageToastRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import MessageToast from '@/components/MessageToast.vue'
import { useConfirm, useMessage, useDeleteConfirm } from '@/composables/useDialog'
import { usePermission } from '@/composables/usePermission'
import { 
  getRoleList, 
  getRoleDetail, 
  addRole, 
  updateRole, 
  deleteRole as deleteRoleApi,
  getPermissionMenu 
} from '@/services/roleApi'
import type { Role, RoleFormData } from '@/services/roleApi'

// 对话框组合式函数
const { confirmDialogState, handleConfirm, handleCancel } = useConfirm()
const { setMessageToastRef, success, error, warning } = useMessage()
const { confirmDelete } = useDeleteConfirm()
const { hasPermission } = usePermission()

// 消息提示组件引用
const messageToastRef = ref<any>(null)

// 角色管理相关变量
const showAddRoleModal = ref(false)
const showEditRoleModal = ref(false)
const showPermissionsModal = ref(false)
const currentRole = ref<Role | null>(null)
const loading = ref(false)

// 初始化消息提示
onMounted(() => {
  setMessageToastRef(messageToastRef.value)
  fetchRoleList()
})

// 角色表单数据
const roleForm = ref<RoleFormData>({
  role_name: '',
  description: '',
  permissions: [],
  status: 1
})

// 角色列表数据
const roleList = ref<Role[]>([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 权限菜单数据（从API获取）
const permissionMenu = ref<any[]>([])
// 选中的权限
const selectedPermissions = ref<string[]>([])
// 展开的节点
const expandedNodes = ref<number[]>([])
// checkbox引用映射
const checkboxRefs = ref<Map<number, HTMLInputElement>>(new Map())

// 设置checkbox引用
const setCheckboxRef = (el: HTMLInputElement | null, menuId: number) => {
  if (el) {
    checkboxRefs.value.set(menuId, el)
    // 设置indeterminate状态
    updateCheckboxState(menuId)
  }
}

// 更新checkbox状态
const updateCheckboxState = (menuId: number) => {
  const checkbox = checkboxRefs.value.get(menuId)
  if (checkbox) {
    const menu = findMenuById(permissionMenu.value, menuId)
    if (menu) {
      checkbox.indeterminate = isMenuIndeterminate(menu)
    }
  }
}

// 根据ID查找菜单
const findMenuById = (menus: any[], id: number): any => {
  for (const menu of menus) {
    if (menu.id === id) return menu
    if (menu.cs_menu_recursive) {
      const found = findMenuById(menu.cs_menu_recursive, id)
      if (found) return found
    }
  }
  return null
}

// 获取有子菜单的子菜单项
const getSubMenusWithChildren = (menus: any[]): any[] => {
  return menus.filter(menu => menu.cs_menu_recursive && menu.cs_menu_recursive.length > 0)
}

// 获取叶子权限项（没有子菜单的项）
const getLeafPermissions = (menus: any[]): any[] => {
  return menus.filter(menu => !menu.cs_menu_recursive || menu.cs_menu_recursive.length === 0)
}

// 切换节点展开状态
const toggleExpand = (nodeId: number) => {
  const index = expandedNodes.value.indexOf(nodeId)
  if (index > -1) {
    expandedNodes.value.splice(index, 1)
  } else {
    expandedNodes.value.push(nodeId)
  }
}

// 检查菜单是否全选
const isMenuAllSelected = (menu: any): boolean => {
  const allIds = getAllMenuIds(menu)
  return allIds.length > 0 && allIds.every(id => selectedPermissions.value.includes(id.toString()))
}

// 检查菜单是否半选状态
const isMenuIndeterminate = (menu: any): boolean => {
  const allIds = getAllMenuIds(menu)
  const selectedCount = allIds.filter(id => selectedPermissions.value.includes(id.toString())).length
  return selectedCount > 0 && selectedCount < allIds.length
}

// 获取菜单下所有权限ID（只包含叶子节点）
const getAllMenuIds = (menu: any): number[] => {
  const ids: number[] = []

  const collectLeafIds = (menuItem: any) => {
    if (menuItem.cs_menu_recursive && menuItem.cs_menu_recursive.length > 0) {
      // 如果有子菜单，递归处理
      menuItem.cs_menu_recursive.forEach((child: any) => {
        collectLeafIds(child)
      })
    } else {
      // 如果没有子菜单，说明它是叶子节点（权限项）
      ids.push(menuItem.id)
    }
  }

  collectLeafIds(menu)
  return ids
}

// 切换菜单权限
const toggleMenuPermissions = (menu: any, checked: boolean) => {
  const allIds = getAllMenuIds(menu)

  if (checked) {
    // 添加该菜单下所有权限
    allIds.forEach(id => {
      const idStr = id.toString()
      if (!selectedPermissions.value.includes(idStr)) {
        selectedPermissions.value.push(idStr)
      }
    })
  } else {
    // 移除该菜单下所有权限
    allIds.forEach(id => {
      const idStr = id.toString()
      const index = selectedPermissions.value.indexOf(idStr)
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    })
  }

  // 更新所有相关checkbox的状态
  updateAllCheckboxStates()
}

// 更新所有checkbox状态
const updateAllCheckboxStates = () => {
  checkboxRefs.value.forEach((checkbox, menuId) => {
    updateCheckboxState(menuId)
  })
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// API调用方法
const fetchRoleList = async () => {
  try {
    loading.value = true
    const response = await getRoleList({
      page: pagination.value.current,
      page_size: pagination.value.pageSize
    })
    roleList.value = response.itemList || []
    pagination.value.total = response.total || 0
  } catch (err) {
    console.error('获取角色列表失败:', err)
    error('获取角色列表失败')
  } finally {
    loading.value = false
  }
}

const fetchPermissionMenu = async () => {
  try {
    const response = await getPermissionMenu()
    permissionMenu.value = response.menu || []
  } catch (err) {
    console.error('获取权限菜单失败:', err)
    error('获取权限菜单失败')
  }
}

// 角色管理方法
const editRole = async (role: Role) => {
  try {
    loading.value = true
    const response = await getRoleDetail(role.id)
    currentRole.value = response
    roleForm.value = {
      id: response.id,
      role_name: response.role_name,
      description: response.description || '',
      permissions: response.permissions || [],
      status: response.status
    }
    showEditRoleModal.value = true
  } catch (err) {
    console.error('获取角色详情失败:', err)
    error('获取角色详情失败')
  } finally {
    loading.value = false
  }
}

const deleteRole = async (role: Role) => {
  const confirmed = await confirmDelete(role.role_name, '角色')
  if (confirmed) {
    try {
      loading.value = true
      await deleteRoleApi(role.id)
      success(`角色"${role.role_name}"删除成功`)
      fetchRoleList() // 刷新列表
    } catch (err) {
      console.error('删除角色失败:', err)
      error('删除角色失败')
    } finally {
      loading.value = false
    }
  }
}

const saveRole = async () => {
  // 验证表单
  if (!roleForm.value.role_name.trim()) {
    error('请输入角色名称')
    return
  }

  try {
    loading.value = true
    if (roleForm.value.id) {
      // 编辑模式
      await updateRole(roleForm.value)
      success(`角色"${roleForm.value.role_name}"修改成功`)
      showEditRoleModal.value = false
    } else {
      // 新增模式
      await addRole(roleForm.value)
      success(`角色"${roleForm.value.role_name}"创建成功`)
      showAddRoleModal.value = false
    }
    
    // 重置表单并刷新列表
    resetRoleForm()
    fetchRoleList()
  } catch (err) {
    console.error('保存角色失败:', err)
    error('保存角色失败')
  } finally {
    loading.value = false
  }
}

const resetRoleForm = () => {
  roleForm.value = {
    role_name: '',
    description: '',
    permissions: [],
    status: 1
  }
  currentRole.value = null
}

const managePermissions = async (role: Role) => {
  currentRole.value = role
  try {
    loading.value = true
    // 获取角色详情（包含权限信息和菜单）
    const response = await getRoleDetail(role.id)

    // 设置菜单数据
    permissionMenu.value = response.menu || []

    // 初始化展开状态
    expandedNodes.value = []
    permissionMenu.value.forEach(menu => {
      if (!expandedNodes.value.includes(menu.id)) {
        expandedNodes.value.push(menu.id)
      }
      // 如果有子菜单，也展开第一个子菜单
      if (menu.cs_menu_recursive && menu.cs_menu_recursive.length > 0) {
        menu.cs_menu_recursive.forEach(child => {
          if (!expandedNodes.value.includes(child.id)) {
            expandedNodes.value.push(child.id)
          }
        })
      }
    })

    // 处理权限数据
    if (response.role_permission) {
      if (response.role_permission === '*') {
        // 如果是超级管理员，获取所有权限
        selectedPermissions.value = response.menu ? getAllPermissionIds(response.menu) : []
      } else {
        // 普通角色，解析权限字符串
        selectedPermissions.value = response.role_permission.split(',').filter(Boolean)
      }
    } else {
      selectedPermissions.value = []
    }

    showPermissionsModal.value = true

    // 延迟更新checkbox状态，确保DOM已渲染
    setTimeout(() => {
      updateAllCheckboxStates()
    }, 100)
  } catch (err) {
    console.error('获取角色权限失败:', err)
    error('获取角色权限失败')
  } finally {
    loading.value = false
  }
}

// 获取所有权限ID
const getAllPermissionIds = (menus: any[]): string[] => {
  const ids: string[] = []
  const traverse = (items: any[]) => {
    items.forEach(item => {
      ids.push(item.id.toString())
      if (item.cs_menu_recursive && item.cs_menu_recursive.length > 0) {
        traverse(item.cs_menu_recursive)
      }
    })
  }
  traverse(menus)
  return ids
}



const savePermissions = async () => {
  if (!currentRole.value) return
  
  try {
    loading.value = true
    const formData: RoleFormData = {
      id: currentRole.value.id,
      role_name: currentRole.value.role_name,
      description: currentRole.value.description || '',
      permissions: selectedPermissions.value,
      role_permission: selectedPermissions.value.join(','),
      status: currentRole.value.status
    }
    
    await updateRole(formData)
    success(`已为角色"${currentRole.value.role_name}"保存权限设置`)
    showPermissionsModal.value = false
    fetchRoleList() // 刷新列表
  } catch (err) {
    console.error('保存权限失败:', err)
    error('保存权限失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  --shadow-light: rgba(13, 27, 42, 0.08);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-deep: rgba(13, 27, 42, 0.25);
}

.page-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-description {
  color: var(--text-light);
  font-size: 14px;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-light);
  border: 1px solid #e4e7ed;
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-actions {
  display: flex;
  gap: 10px;
}

/* .card-body {
  padding: 24px;
} */

/* 表格样式 */
.table-container {
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.table th {
  background: var(--secondary-light-blue-gray);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
  position: sticky;
  top: 0;
  z-index: 1;
}

.table td {
  color: var(--text-secondary);
  font-size: 13px;
}

.table tr:hover {
  background: rgba(65, 90, 119, 0.05);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-primary {
  background: var(--primary-steel-blue);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-business-blue);
}

.btn-secondary {
  background: var(--secondary-elegant-blue);
  color: var(--text-secondary);
}

.btn-secondary:hover {
  background: var(--secondary-steel-blue);
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.icon {
  font-size: 14px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-lg {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 角色管理样式 */
.role-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
}

.required {
  color: #dc3545;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-steel-blue);
  box-shadow: 0 0 0 2px rgba(52, 144, 220, 0.1);
}

/* 权限管理样式 */
.permissions-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.permission-tree {
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: white;
}

.permission-tree:last-child {
  margin-bottom: 0;
}

.tree-node {
  border-bottom: 1px solid #f0f0f0;
}

.tree-node:last-child {
  border-bottom: none;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  transition: background-color 0.2s ease;
}

.node-content:hover {
  background-color: #f8f9fa;
}

.parent-node {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

.child-node .node-content {
  padding-left: 32px;
  background-color: #fafbfc;
}

.expand-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  transition: transform 0.2s ease;
  user-select: none;
}

.expand-icon.expanded {
  transform: rotate(0deg);
}

.expand-placeholder {
  width: 20px;
  margin-right: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-primary);
  flex: 1;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

.children-nodes {
  border-top: 1px solid #e9ecef;
}

.permission-items {
  padding: 8px 16px 16px 32px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  background-color: #fafbfc;
}

/* 子菜单下的权限项 */
.child-node .permission-items {
  padding: 8px 16px 16px 48px;
}

.permission-item {
  padding: 6px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: normal;
  font-size: 13px;
}

.permission-item:hover {
  background-color: #e9ecef;
}

.permission-item input[type="checkbox"] {
  margin-right: 6px;
  transform: scale(0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .card-actions {
    flex-direction: column;
  }

  .table-container {
    overflow-x: auto;
  }

  .modal-dialog {
    width: 95%;
    margin: 16px;
  }

  .permission-items {
    grid-template-columns: 1fr;
  }
}
</style>