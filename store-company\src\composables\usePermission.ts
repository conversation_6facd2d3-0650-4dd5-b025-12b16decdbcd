import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { MenuInfo, PermissionCheckResult } from '@/types/auth'

/**
 * 权限管理组合函数
 * 提供权限检查、菜单过滤等功能
 */
export function usePermission() {
  const authStore = useAuthStore()

  /**
   * 检查用户是否拥有指定权限
   * @param permission 权限标识
   * @returns 是否拥有权限
   */
  const hasPermission = (permission: string): boolean => {
    if (!authStore.user?.permissions) {
      return false
    }
    return authStore.user.permissions.includes(permission)
  }

  /**
   * 检查用户是否拥有任意一个权限
   * @param permissions 权限标识数组
   * @returns 是否拥有任意一个权限
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!authStore.user?.permissions || permissions.length === 0) {
      return false
    }
    return permissions.some(permission => authStore.user!.permissions!.includes(permission))
  }

  /**
   * 检查用户是否拥有所有权限
   * @param permissions 权限标识数组
   * @returns 是否拥有所有权限
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!authStore.user?.permissions || permissions.length === 0) {
      return false
    }
    return permissions.every(permission => authStore.user!.permissions!.includes(permission))
  }

  /**
   * 检查用户是否可以访问指定菜单
   * @param menuSign 菜单标识
   * @returns 是否可以访问
   */
  const canAccessMenu = (menuSign: string): boolean => {
    if (!menuSign) return true // 没有菜单标识的认为是公开菜单
    return hasPermission(menuSign)
  }

  /**
   * 检查用户是否可以访问指定路由
   * @param routeName 路由名称
   * @returns 权限检查结果
   */
  const canAccessRoute = (routeName: string): PermissionCheckResult => {
    // 定义路由权限映射
    const routePermissionMap: Record<string, string> = {
      'permissions-admin': 'admin_manage',
      'permissions-role': 'role_manage',
      'shops': 'shop_manage',
      'dashboard': '' // 数据大屏不需要特殊权限
    }

    const requiredPermission = routePermissionMap[routeName]
    
    if (!requiredPermission) {
      return { hasPermission: true }
    }

    const hasAccess = hasPermission(requiredPermission)
    return {
      hasPermission: hasAccess,
      reason: hasAccess ? undefined : `缺少权限: ${requiredPermission}`
    }
  }

  /**
   * 根据权限过滤菜单列表
   * @param menus 菜单列表
   * @returns 过滤后的菜单列表
   */
  const filterMenusByPermission = (menus: MenuInfo[]): MenuInfo[] => {
    if (!authStore.user?.permissions) {
      return []
    }

    const filterMenu = (menu: MenuInfo): MenuInfo | null => {
      // 如果菜单有权限标识，检查权限
      if (menu.menu_sign && !hasPermission(menu.menu_sign)) {
        return null
      }

      // 递归过滤子菜单
      const filteredChildren = menu.children
        ? menu.children.map(filterMenu).filter(Boolean) as MenuInfo[]
        : undefined

      return {
        ...menu,
        children: filteredChildren
      }
    }

    return menus.map(filterMenu).filter(Boolean) as MenuInfo[]
  }

  /**
   * 获取用户可访问的菜单列表
   */
  const accessibleMenus = computed(() => {
    if (!authStore.user?.menus) {
      return []
    }
    return filterMenusByPermission(authStore.user.menus)
  })

  /**
   * 获取第一个可访问的菜单路径
   */
  const getFirstAccessibleMenuPath = (): string => {
    const menus = accessibleMenus.value
    if (menus.length === 0) {
      return '/dashboard' // 默认返回数据大屏
    }

    // 递归查找第一个叶子菜单
    const findFirstLeaf = (menuList: MenuInfo[]): string => {
      for (const menu of menuList) {
        if (!menu.children || menu.children.length === 0) {
          // 根据菜单标识映射到路由路径
          const routeMap: Record<string, string> = {
            'dashboard': '/dashboard',
            'shop_manage': '/shops',
            'admin_manage': '/permissions/admin',
            'role_manage': '/permissions/role'
          }
          return routeMap[menu.menu_sign] || '/dashboard'
        } else {
          const childPath = findFirstLeaf(menu.children)
          if (childPath) return childPath
        }
      }
      return '/dashboard'
    }

    return findFirstLeaf(menus)
  }

  /**
   * 检查是否为超级管理员
   */
  const isSuperAdmin = computed(() => {
    return authStore.user?.role_name === '超级管理员' || 
           authStore.user?.role_id === 1
  })

  /**
   * 获取用户权限列表
   */
  const userPermissions = computed(() => {
    return authStore.user?.permissions || []
  })

  /**
   * 获取用户菜单列表
   */
  const userMenus = computed(() => {
    return authStore.user?.menus || []
  })

  return {
    // 权限检查
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessMenu,
    canAccessRoute,
    
    // 菜单过滤
    filterMenusByPermission,
    accessibleMenus,
    getFirstAccessibleMenuPath,
    
    // 状态
    isSuperAdmin,
    userPermissions,
    userMenus
  }
}
