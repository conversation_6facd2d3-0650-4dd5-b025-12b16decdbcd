<?php

namespace app\validate;

/**
 * 权限相关验证规则
 * 
 * 提供权限系统相关的数据验证功能
 */
class PermissionValidate extends BaseValidate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'menu_sign' => 'require|length:3,100|alphaDash',
        'company_id' => 'require|integer|gt:0',
        'admin_id' => 'require|integer|gt:0',
        'role_id' => 'require|integer|gt:0',
        'menu_type' => 'require|integer|in:0,1,2',
        'permissions' => 'array',
        'permission_ids' => 'array',
        'cache_key' => 'require|length:5,200|alphaDash',
        'route_controller' => 'require|length:3,100',
        'route_action' => 'require|length:2,50|alphaDash',
        'batch_menu_signs' => 'require|array|min:1',
        'cache_ttl' => 'integer|between:60,86400',
    ];

    /**
     * 验证消息
     */
    protected $message = [
        'menu_sign.require' => '权限标识不能为空',
        'menu_sign.length' => '权限标识长度必须在3-100个字符之间',
        'menu_sign.alphaDash' => '权限标识只能包含字母、数字、下划线和破折号',
        'company_id.require' => '公司ID不能为空',
        'company_id.integer' => '公司ID必须为整数',
        'company_id.gt' => '公司ID必须大于0',
        'admin_id.require' => '管理员ID不能为空',
        'admin_id.integer' => '管理员ID必须为整数',
        'admin_id.gt' => '管理员ID必须大于0',
        'role_id.require' => '角色ID不能为空',
        'role_id.integer' => '角色ID必须为整数',
        'role_id.gt' => '角色ID必须大于0',
        'menu_type.require' => '菜单类型不能为空',
        'menu_type.integer' => '菜单类型必须为整数',
        'menu_type.in' => '菜单类型必须为0（平台）、1（公司）或2（门店）',
        'permissions.array' => '权限数据必须为数组格式',
        'permission_ids.array' => '权限ID列表必须为数组格式',
        'cache_key.require' => '缓存键不能为空',
        'cache_key.length' => '缓存键长度必须在5-200个字符之间',
        'cache_key.alphaDash' => '缓存键只能包含字母、数字、下划线和破折号',
        'route_controller.require' => '控制器名称不能为空',
        'route_controller.length' => '控制器名称长度必须在3-100个字符之间',
        'route_action.require' => '方法名称不能为空',
        'route_action.length' => '方法名称长度必须在2-50个字符之间',
        'route_action.alphaDash' => '方法名称只能包含字母、数字、下划线和破折号',
        'batch_menu_signs.require' => '批量权限标识不能为空',
        'batch_menu_signs.array' => '批量权限标识必须为数组format',
        'batch_menu_signs.min' => '批量权限标识至少需要1个',
        'cache_ttl.integer' => '缓存TTL必须为整数',
        'cache_ttl.between' => '缓存TTL必须在60-86400秒之间',
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        // 权限检查场景
        'check_permission' => ['menu_sign', 'company_id', 'admin_id', 'role_id'],
        
        // 批量权限检查场景
        'check_permissions' => ['batch_menu_signs', 'company_id', 'admin_id', 'role_id'],
        
        // 获取用户权限场景
        'get_user_permissions' => ['company_id', 'admin_id'],
        
        // 获取角色权限场景
        'get_role_permissions' => ['company_id', 'role_id'],
        
        // 菜单类型验证场景
        'menu_type' => ['menu_type'],
        
        // 路由权限映射场景
        'route_permission' => ['route_controller', 'route_action'],
        
        // 缓存操作场景
        'cache_operation' => ['cache_key', 'company_id'],
        
        // 缓存设置场景
        'cache_set' => ['cache_key', 'cache_ttl'],
        
        // 权限数据验证场景
        'permission_data' => ['permissions'],
        
        // 权限ID列表验证场景
        'permission_ids' => ['permission_ids'],
    ];

    /**
     * 验证权限标识格式
     * 
     * @param string $value 权限标识
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkMenuSign($value, $rule, $data)
    {
        // 检查基本格式
        if (!preg_match('/^[a-z][a-z0-9_]*[a-z0-9]$/', $value)) {
            return '权限标识格式不正确，必须以字母开头，以字母或数字结尾，中间只能包含小写字母、数字和下划线';
        }

        // 检查长度
        if (strlen($value) < 3 || strlen($value) > 100) {
            return '权限标识长度必须在3-100个字符之间';
        }

        // 检查是否包含连续下划线
        if (strpos($value, '__') !== false) {
            return '权限标识不能包含连续的下划线';
        }

        // 检查保留关键字
        $reservedWords = ['admin', 'root', 'system', 'config', 'database', 'cache', 'session'];
        if (in_array($value, $reservedWords)) {
            return '权限标识不能使用保留关键字';
        }

        return true;
    }

    /**
     * 验证公司ID是否有效
     * 
     * @param int $value 公司ID
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkCompanyId($value, $rule, $data)
    {
        if (!is_numeric($value) || $value <= 0) {
            return '公司ID必须为大于0的整数';
        }

        // 可以在这里添加数据库检查，验证公司是否存在
        // 但为了避免循环依赖，这里只做基本格式验证

        return true;
    }

    /**
     * 验证管理员ID是否有效
     * 
     * @param int $value 管理员ID
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkAdminId($value, $rule, $data)
    {
        if (!is_numeric($value) || $value <= 0) {
            return '管理员ID必须为大于0的整数';
        }

        return true;
    }

    /**
     * 验证角色ID是否有效
     * 
     * @param int $value 角色ID
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkRoleId($value, $rule, $data)
    {
        if (!is_numeric($value) || $value <= 0) {
            return '角色ID必须为大于0的整数';
        }

        return true;
    }

    /**
     * 验证权限数据数组格式
     * 
     * @param array $value 权限数据
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkPermissions($value, $rule, $data)
    {
        if (!is_array($value)) {
            return '权限数据必须为数组格式';
        }

        // 检查必要的键
        $requiredKeys = ['menu_ids', 'menu_signs'];
        foreach ($requiredKeys as $key) {
            if (!isset($value[$key])) {
                return "权限数据缺少必要的键：{$key}";
            }

            if (!is_array($value[$key])) {
                return "权限数据的{$key}必须为数组格式";
            }
        }

        // 验证menu_ids数组
        foreach ($value['menu_ids'] as $menuId) {
            if (!is_numeric($menuId) || $menuId <= 0) {
                return '权限数据中的menu_ids必须为大于0的整数数组';
            }
        }

        // 验证menu_signs数组
        foreach ($value['menu_signs'] as $menuSign) {
            if (!is_string($menuSign) || empty($menuSign)) {
                return '权限数据中的menu_signs必须为非空字符串数组';
            }

            // 使用内置方法验证每个权限标识
            $checkResult = $this->checkMenuSign($menuSign, null, $data);
            if ($checkResult !== true) {
                return "权限数据中的权限标识格式错误：{$checkResult}";
            }
        }

        return true;
    }

    /**
     * 验证批量权限标识
     * 
     * @param array $value 权限标识数组
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkBatchMenuSigns($value, $rule, $data)
    {
        if (!is_array($value) || empty($value)) {
            return '批量权限标识必须为非空数组';
        }

        if (count($value) > 100) {
            return '批量权限标识数量不能超过100个';
        }

        foreach ($value as $menuSign) {
            if (!is_string($menuSign)) {
                return '批量权限标识中的每一项都必须为字符串';
            }

            $checkResult = $this->checkMenuSign($menuSign, null, $data);
            if ($checkResult !== true) {
                return "批量权限标识中的权限标识格式错误：{$checkResult}";
            }
        }

        // 检查是否有重复
        if (count($value) !== count(array_unique($value))) {
            return '批量权限标识中不能有重复项';
        }

        return true;
    }

    /**
     * 验证缓存键格式
     * 
     * @param string $value 缓存键
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkCacheKey($value, $rule, $data)
    {
        // 检查基本格式
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_:]*[a-zA-Z0-9]$/', $value)) {
            return '缓存键格式不正确，必须以字母开头，以字母或数字结尾，中间只能包含字母、数字、下划线和冒号';
        }

        // 检查长度
        if (strlen($value) < 5 || strlen($value) > 200) {
            return '缓存键长度必须在5-200个字符之间';
        }

        // 检查是否包含连续的特殊字符
        if (preg_match('/[_:]{2,}/', $value)) {
            return '缓存键不能包含连续的下划线或冒号';
        }

        return true;
    }

    /**
     * 验证控制器名称格式
     * 
     * @param string $value 控制器名称
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkRouteController($value, $rule, $data)
    {
        // 移除可能的命名空间前缀
        $controllerName = basename(str_replace('\\', '/', $value));

        // 检查控制器名称格式
        if (!preg_match('/^[A-Z][a-zA-Z0-9]*Controller$/', $controllerName)) {
            return '控制器名称必须以大写字母开头，以Controller结尾，中间只能包含字母和数字';
        }

        if (strlen($controllerName) < 10) { // 至少包含Controller后缀
            return '控制器名称太短';
        }

        return true;
    }

    /**
     * 验证方法名称格式
     * 
     * @param string $value 方法名称
     * @param mixed $rule 验证规则
     * @param array $data 完整数据
     * @return bool|string 验证结果
     */
    protected function checkRouteAction($value, $rule, $data)
    {
        // 检查方法名称格式
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $value)) {
            return '方法名称必须以字母开头，只能包含字母、数字和下划线';
        }

        // 排除PHP保留方法名
        $reservedMethods = [
            '__construct', '__destruct', '__call', '__callStatic',
            '__get', '__set', '__isset', '__unset', '__sleep',
            '__wakeup', '__toString', '__invoke', '__clone'
        ];

        if (in_array($value, $reservedMethods)) {
            return '方法名称不能使用PHP保留方法名';
        }

        return true;
    }
}