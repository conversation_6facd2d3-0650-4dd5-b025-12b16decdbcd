import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LoginForm, RegisterForm, User } from '@/types/auth'
import { ElMessage } from 'element-plus'
import { login as apiLogin, getUserInfo, logout as apiLogout, getUserPermissions } from '@/services/authApi'
import type { LoginResponse } from '@/services/api'
import { MD5 } from 'crypto-js'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('company_token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 登录
  const login = async (form: LoginForm) => {
    try {
      loading.value = true

      const response: LoginResponse = await apiLogin({
        username: form.username,
        password: MD5(form.password).toString()
      })

      // 保存token和用户信息
      token.value = response.token
      user.value = {
        id: response.id,
        username: response.username,
        nickname: response.nickname,
        avatar: response.avatar,
        role_id: response.role_id, // 根据实际API返回调整
        role_name: response.role_text, // 根据实际API返回调整
        status: 1,
        created_at: '',
        permissions: response.permissions || [], // 权限列表
        menus: response.menu || [] // 菜单列表
      }

      localStorage.setItem('company_token', token.value)
      localStorage.setItem('company_user_info', JSON.stringify(user.value))

      ElMessage.success('登录成功')
      return true
    } catch (error) {
      console.error('登录失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册（暂时保留模拟实现）
  const register = async (form: RegisterForm) => {
    try {
      loading.value = true

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success('注册成功，请登录')
      return true
    } catch (error) {
      ElMessage.error('注册失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用API退出登录
      await apiLogout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地数据
      token.value = ''
      user.value = null
      localStorage.removeItem('company_token')
      localStorage.removeItem('company_user_info')
      ElMessage.success('已退出登录')
    }
  }

  // 初始化用户信息（如果有token）
  const initUserInfo = async () => {
    if (token.value) {
      try {
        // 总是从API获取最新的用户信息（包括套餐状态）
        const userInfo = await getUserInfo()
        user.value = userInfo
        localStorage.setItem('company_user_info', JSON.stringify(userInfo))
        // 获取权限信息
        await loadUserPermissions()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取用户信息失败，清除token
        token.value = ''
        localStorage.removeItem('company_token')
        localStorage.removeItem('company_user_info')
      }
    }
  }

  // 加载用户权限信息
  const loadUserPermissions = async () => {
    try {
      // 开发环境下使用模拟数据
      if (import.meta.env.DEV) {
        const { mockUserPermissions } = await import('@/utils/mockPermissions')
        const mockData = mockUserPermissions(user.value?.role_name || '超级管理员')
        if (user.value) {
          user.value.permissions = mockData.permissions
          user.value.menus = mockData.menus
          // 更新localStorage中的用户信息
          localStorage.setItem('company_user_info', JSON.stringify(user.value))
          console.log('🔧 开发环境：使用模拟权限数据', mockData.permissions)
        }
        return
      }

      // 生产环境下从API获取
      const permissionData = await getUserPermissions()
      if (user.value) {
        user.value.permissions = permissionData.permissions
        user.value.menus = permissionData.menus
        // 更新localStorage中的用户信息
        localStorage.setItem('company_user_info', JSON.stringify(user.value))
      }
    } catch (error) {
      console.error('获取用户权限失败:', error)
      // 如果获取权限失败，设置为空数组
      if (user.value) {
        user.value.permissions = []
        user.value.menus = []
      }
    }
  }

  return {
    token,
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    initUserInfo,
    loadUserPermissions
  }
})