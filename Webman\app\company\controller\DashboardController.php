<?php

namespace app\company\controller;

use app\model\CsCompany;
use app\model\CsShop;
use app\model\CsOrder;
use app\model\CsOrderGoods;
use app\model\CsGoods;
use app\model\CsUser;
use app\model\CsInventory;
use app\model\CsPaymentMethod;
use support\Request;
use support\Response;
use support\Log;
use support\Db;

class DashboardController
{
    /**
     * 获取仪表板概览数据
     * @param Request $request
     * @return Response
     */
    public function overview(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            
            // 获取门店总数和在线门店数
            $totalShops = CsShop::where('company_id', $companyId)->count();
            $onlineShops = CsShop::where('company_id', $companyId)->where('status', 1)->count();
            
            // 获取本月新增门店数
            $newShopsThisMonth = CsShop::where('company_id', $companyId)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->count();
            
            // 获取今日营业额
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            $todayRevenue = CsOrder::whereIn('shop_id', $shopIds)
                ->whereDate('created_at', date('Y-m-d'))
                ->where('order_status', '>=', 2) // 已完成的订单
                ->sum('real_pay_money');
            
            // 获取今日订单数
            $todayOrders = CsOrder::whereIn('shop_id', $shopIds)
                ->whereDate('created_at', date('Y-m-d'))
                ->count();
            
            // 获取成功订单数
            $successOrders = CsOrder::whereIn('shop_id', $shopIds)
                ->whereDate('created_at', date('Y-m-d'))
                ->where('order_status', '>=', 2)
                ->count();
            
            // 获取活跃会员数（本月有消费的会员）
            $activeMembers = CsOrder::whereIn('shop_id', $shopIds)
                ->whereMonth('created_at', date('m'))
                ->whereYear('created_at', date('Y'))
                ->where('user_id', '>', 0)
                ->distinct('user_id')
                ->count();
            
            return success([
                'totalShops' => $totalShops,
                'onlineShops' => $onlineShops,
                'newShopsThisMonth' => $newShopsThisMonth,
                'todayRevenue' => floatval($todayRevenue),
                'todayOrders' => $todayOrders,
                'successOrders' => $successOrders,
                'activeMembers' => $activeMembers
            ]);
        } catch (\Exception $e) {
            Log::error('获取仪表板概览数据失败: ' . $e->getMessage());
            return fail('获取数据失败');
        }
    }

    /**
     * 获取营业额趋势数据
     * @param Request $request
     * @return Response
     */
    public function revenueTrend(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today'); // today, week, month, year
            
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();
            $query = CsOrder::whereIn('shop_id', $shopIds)->where('order_status', '>=', 2);
            
            $data = [];
            $xAxis = [];
            
            switch ($period) {
                case 'today':
                    // 按小时统计今日数据
                    for ($hour = 0; $hour < 24; $hour += 4) {
                        $startHour = str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00';
                        $endHour = str_pad($hour + 4, 2, '0', STR_PAD_LEFT) . ':00';
                        $xAxis[] = $startHour;
                        
                        $revenue = (clone $query)
                            ->whereDate('created_at', date('Y-m-d'))
                            ->whereTime('created_at', '>=', $startHour)
                            ->whereTime('created_at', '<', $endHour)
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
                    
                case 'week':
                    // 按天统计本周数据
                    for ($i = 6; $i >= 0; $i--) {
                        $date = date('Y-m-d', strtotime("-{$i} days"));
                        $dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date('w', strtotime($date))];
                        $xAxis[] = $dayName;
                        
                        $revenue = (clone $query)
                            ->whereDate('created_at', $date)
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
                    
                case 'month':
                    // 按周统计本月数据
                    $startOfMonth = date('Y-m-01');
                    $endOfMonth = date('Y-m-t');
                    $weeks = [];
                    
                    $current = strtotime($startOfMonth);
                    $end = strtotime($endOfMonth);
                    $weekNum = 1;
                    
                    while ($current <= $end) {
                        $weekStart = $current;
                        $weekEnd = min(strtotime('+6 days', $current), $end);
                        
                        $xAxis[] = date('j', $weekStart) . '日';
                        
                        $revenue = (clone $query)
                            ->whereBetween('created_at', [
                                date('Y-m-d 00:00:00', $weekStart),
                                date('Y-m-d 23:59:59', $weekEnd)
                            ])
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                        
                        $current = strtotime('+7 days', $current);
                        $weekNum++;
                    }
                    break;
                    
                case 'year':
                    // 按月统计本年数据
                    for ($month = 1; $month <= 12; $month++) {
                        $xAxis[] = $month . '月';
                        
                        $revenue = (clone $query)
                            ->whereMonth('created_at', $month)
                            ->whereYear('created_at', date('Y'))
                            ->sum('real_pay_money');
                        $data[] = floatval($revenue);
                    }
                    break;
            }
            
            return success([
                'period' => $period,
                'xAxis' => $xAxis,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取营业额趋势数据失败: ' . $e->getMessage());
            return fail('获取数据失败');
        }
    }

    /**
     * 获取门店业绩对比数据
     * @param Request $request
     * @return Response
     */
    public function shopComparison(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');
            
            $shops = CsShop::where('company_id', $companyId)
                ->where('status', 1)
                ->select('id', 'shop_name')
                ->get();
            
            $shopNames = [];
            $revenues = [];
            
            foreach ($shops as $shop) {
                $shopNames[] = $shop->shop_name;
                
                $query = CsOrder::where('shop_id', $shop->id)->where('order_status', '>=', 2);
                $this->applyPeriodFilter($query, $period);
                
                $revenue = $query->sum('real_pay_money');
                $revenues[] = floatval($revenue);
            }
            
            return success([
                'period' => $period,
                'shopNames' => $shopNames,
                'revenues' => $revenues
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店业绩对比数据失败: ' . $e->getMessage());
            return fail('获取数据失败');
        }
    }

    /**
     * 获取支付方式分析数据
     * @param Request $request
     * @return Response
     */
    public function paymentAnalysis(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');

            // 获取该企业下所有门店
            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();

            if (empty($shopIds)) {
                // 没有门店，返回空数据
                return success([
                    'period' => $period,
                    'total_amount' => 0,
                    'payment_methods' => []
                ]);
            }

            // 构建订单查询
            $query = CsOrder::whereIn('shop_id', $shopIds)
                ->where('order_status', '>=', 2);
            $this->applyPeriodFilter($query, $period);

            $totalAmount = $query->sum('real_pay_money');

            // 获取各支付方式的金额统计
            $paymentStats = (clone $query)
                ->whereNotNull('payment_type')
                ->where('payment_type', '>', 0)
                ->selectRaw('payment_type, SUM(real_pay_money) as amount, COUNT(*) as count')
                ->groupBy('payment_type')
                ->get();

            // 如果没有支付统计数据，返回空数据
            if ($paymentStats->isEmpty() || $totalAmount == 0) {
                return success([
                    'period' => $period,
                    'total_amount' => 0,
                    'payment_methods' => []
                ]);
            }

            // 获取支付方式名称映射
            $allPaymentMethods = CsPaymentMethod::whereIn('shop_id', $shopIds)
                ->where('status', 1)
                ->select('id', 'payment_method_name')
                ->get()
                ->keyBy('id');

            // 构建返回数据
            $result = [];
            foreach ($paymentStats as $stat) {
                $paymentTypeId = $stat->payment_type;
                $percentage = round(($stat->amount / $totalAmount) * 100, 1);

                // 获取支付方式名称
                $paymentMethod = $allPaymentMethods->get($paymentTypeId);
                $paymentName = $paymentMethod ? $paymentMethod->payment_method_name : $this->getDefaultPaymentName($paymentTypeId);

                $result[] = [
                    'id' => $paymentTypeId,
                    'name' => $paymentName,
                    'amount' => floatval($stat->amount),
                    'percentage' => $percentage,
                    'count' => intval($stat->count)
                ];
            }

            // 按金额排序
            usort($result, function($a, $b) {
                return $b['amount'] <=> $a['amount'];
            });

            return success([
                'period' => $period,
                'total_amount' => floatval($totalAmount),
                'payment_methods' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取支付方式分析数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }



    /**
     * 获取默认支付方式名称
     * @param int $paymentTypeId
     * @return string
     */
    private function getDefaultPaymentName(int $paymentTypeId): string
    {
        $defaultNames = [
            1 => '微信支付',
            2 => '支付宝',
            3 => '现金支付',
            4 => '银行卡',
            5 => '其他支付'
        ];

        return $defaultNames[$paymentTypeId] ?? '支付方式' . $paymentTypeId;
    }

    /**
     * 获取热销商品排行数据
     * @param Request $request
     * @return Response
     */
    public function productRanking(Request $request): Response
    {
        try {
            $companyId = $request->company_id;
            $period = $request->get('period', 'today');

            $shopIds = CsShop::where('company_id', $companyId)->pluck('id')->toArray();

            if (empty($shopIds)) {
                return success([
                    'period' => $period,
                    'products' => []
                ]);
            }

            // 使用DB::table避免SoftDeletes的影响
            $query = \support\Db::table('cs_order_goodss as og')
                ->join('cs_orders as o', 'og.order_id', '=', 'o.id')
                ->join('cs_goods as g', 'og.goods_id', '=', 'g.id')
                ->whereIn('o.shop_id', $shopIds)
                ->where('o.order_status', '>=', 2);

            // 应用时间过滤
            switch ($period) {
                case 'today':
                    $query->whereDate('o.created_at', date('Y-m-d'));
                    break;
                case 'week':
                    $query->where('o.created_at', '>=', date('Y-m-d 00:00:00', strtotime('-6 days')));
                    break;
                case 'month':
                    $query->whereMonth('o.created_at', date('m'))
                          ->whereYear('o.created_at', date('Y'));
                    break;
                case 'year':
                    $query->whereYear('o.created_at', date('Y'));
                    break;
            }

            $products = $query->selectRaw('
                    g.goods_name as name,
                    SUM(og.goods_nums) as sales,
                    SUM(og.goods_nums * og.goods_price) as revenue
                ')
                ->groupBy('g.id', 'g.goods_name')
                ->orderBy('sales', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($item) {
                    return [
                        'name' => $item->name ?: '未知商品',
                        'sales' => intval($item->sales),
                        'revenue' => floatval($item->revenue)
                    ];
                })
                ->toArray();

            return success([
                'period' => $period,
                'products' => $products
            ]);
        } catch (\Exception $e) {
            Log::error('获取热销商品排行数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取门店实时状态数据
     * @param Request $request
     * @return Response
     */
    public function shopStatus(Request $request): Response
    {
        try {
            $companyId = $request->company_id;

            $shops = CsShop::where('company_id', $companyId)
                ->select('id', 'shop_name', 'status', 'updated_at')
                ->get();

            if ($shops->isEmpty()) {
                return success([
                    'shopList' => []
                ]);
            }

            $shopList = [];

            foreach ($shops as $shop) {
                // 获取今日营业额
                $todayRevenue = CsOrder::where('shop_id', $shop->id)
                    ->whereDate('created_at', date('Y-m-d'))
                    ->where('order_status', '>=', 2)
                    ->sum('real_pay_money');

                // 获取今日订单数
                $todayOrders = CsOrder::where('shop_id', $shop->id)
                    ->whereDate('created_at', date('Y-m-d'))
                    ->count();

                // 计算客单价
                $avgPrice = $todayOrders > 0 ? round($todayRevenue / $todayOrders, 0) : 0;

                // 计算利润（简单按30%利润率计算）
                $profit = round($todayRevenue * 0.3, 0);

                // 获取库存状态（使用正确的表名和字段名）
                $lowStockCount = CsInventory::from('cs_inventory as inv')
                    ->join('cs_goods as g', 'inv.goods_id', '=', 'g.id')
                    ->where('g.shop_id', $shop->id)
                    ->where('inv.current_stock', '<', 'inv.min_stock')
                    ->count();

                $stockStatus = 'normal';
                if ($lowStockCount > 5) {
                    $stockStatus = 'low';
                } elseif ($lowStockCount > 2) {
                    $stockStatus = 'warning';
                }

                // 计算最后更新时间
                $lastUpdate = $this->getTimeAgo($shop->updated_at);

                $shopList[] = [
                    'id' => $shop->id,
                    'name' => $shop->shop_name,
                    'status' => $shop->status == 1 ? 'online' : 'offline',
                    'todayRevenue' => floatval($todayRevenue),
                    'todayOrders' => $todayOrders,
                    'avgPrice' => $avgPrice,
                    'profit' => $profit,
                    'stockStatus' => $stockStatus,
                    'lastUpdate' => $lastUpdate
                ];
            }

            return success([
                'shopList' => $shopList
            ]);
        } catch (\Exception $e) {
            Log::error('获取门店实时状态数据失败: ' . $e->getMessage());
            return fail('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 应用时间段过滤
     * @param $query
     * @param string $period
     * @param string $dateField
     */
    private function applyPeriodFilter($query, string $period, string $dateField = 'created_at'): void
    {
        switch ($period) {
            case 'today':
                $query->whereDate($dateField, date('Y-m-d'));
                break;
            case 'week':
                $query->where($dateField, '>=', date('Y-m-d', strtotime('-6 days')));
                break;
            case 'month':
                $query->whereMonth($dateField, date('m'))
                      ->whereYear($dateField, date('Y'));
                break;
            case 'year':
                $query->whereYear($dateField, date('Y'));
                break;
        }
    }

    /**
     * 获取时间差描述
     * @param string $datetime
     * @return string
     */
    private function getTimeAgo(string $datetime): string
    {
        $time = strtotime($datetime);
        $diff = time() - $time;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } else {
            return floor($diff / 86400) . '天前';
        }
    }
}
