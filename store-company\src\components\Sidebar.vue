<template>
  <div class="sidebar" :class="{ collapsed: isCollapsed }">
    <div class="logo-area">
      <div class="logo">JR</div>
      <div class="logo-text">企业管理系统</div>
    </div>

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <!-- 数据大屏 - 始终显示 -->
      <div class="nav-item">
        <a class="nav-link" :class="{ active: currentPage === 'dashboard' }" @click="navigateTo('dashboard')">
          <div class="nav-icon">📊</div>
          <span class="nav-text">数据大屏</span>
        </a>
      </div>

      <!-- 门店管理 - 需要权限 -->
      <div class="nav-item" v-if="hasPermission('shop_manage')">
        <a class="nav-link" :class="{ active: currentPage === 'shops' }" @click="navigateTo('shops')">
          <div class="nav-icon">🏪</div>
          <span class="nav-text">门店管理</span>
        </a>
      </div>

      <!-- 权限管理 - 需要权限 -->
      <div class="nav-item" v-if="hasPermission('admin_manage') || hasPermission('role_manage')">
        <a class="nav-link" :class="{ active: currentPage.startsWith('permissions') }" @click="togglePermissionsMenu">
          <div class="nav-icon">🔐</div>
          <span class="nav-text">权限管理</span>
          <div class="nav-arrow" :class="{ 'nav-arrow-expanded': showPermissionsMenu }">›</div>
        </a>
        <div v-show="showPermissionsMenu" class="nav-submenu">
          <!-- 管理员管理 -->
          <div class="nav-item" v-if="hasPermission('admin_manage')">
            <a class="nav-link nav-sub-link" :class="{ active: currentPage === 'permissions-admin' }" @click="navigateTo('permissions-admin')">
              <div class="nav-icon">👤</div>
              <span class="nav-text">管理员管理</span>
            </a>
          </div>
          <!-- 角色管理 -->
          <div class="nav-item" v-if="hasPermission('role_manage')">
            <a class="nav-link nav-sub-link" :class="{ active: currentPage === 'permissions-role' }" @click="navigateTo('permissions-role')">
              <div class="nav-icon">🏷️</div>
              <span class="nav-text">角色管理</span>
            </a>
          </div>
        </div>
      </div>


    </nav>

    <!-- 底部联系客服 -->
    <div class="customer-service" v-if="!isCollapsed" @click="showCustomerService">
      <div class="service-icon">
        <span>💬</span>
      </div>
      <div class="service-details">
        <p class="service-title">联系客服</p>
        <p class="service-desc">获得专业支持</p>
      </div>
      <div class="service-arrow">›</div>
    </div>

    <!-- 客服联系方式对话框 -->
    <CustomerServiceDialog 
      v-model="customerServiceVisible" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePermission } from '@/composables/usePermission'
import CustomerServiceDialog from './CustomerServiceDialog.vue'

const router = useRouter()
const route = useRoute()
const { hasPermission } = usePermission()

const isCollapsed = ref(false)
const showPermissionsMenu = ref(false)
const customerServiceVisible = ref(false)

// 开发环境标识
const isDev = import.meta.env.DEV

const currentPage = computed(() => {
  return route.name as string || 'dashboard'
})

const navigateTo = (page: string) => {
  router.push({ name: page })
}

const togglePermissionsMenu = () => {
  showPermissionsMenu.value = !showPermissionsMenu.value
  if (showPermissionsMenu.value && currentPage.value.startsWith('permissions')) {
    // 如果当前在权限管理页面，则直接展开子菜单
    return
  }
  if (!showPermissionsMenu.value) {
    // 如果收起子菜单，则导航到权限管理主页
    navigateTo('permissions')
  }
}

// 显示客服对话框
const showCustomerService = () => {
  customerServiceVisible.value = true
}

// 监听路由变化，自动展开相应菜单
if (currentPage.value.startsWith('permissions')) {
  showPermissionsMenu.value = true
}
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  /* 主要蓝色系 - 深邃商务蓝 */
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  
  /* 辅助蓝色系 - 钢铁蓝与淡雅蓝灰 */
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  
  /* 点缀色系 - 精致金属色 */
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  
  /* 文字颜色系统 */
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  
  /* 背景和效果 */
  --glass-background: rgba(255, 255, 255, 0.92);
  --glass-border: rgba(255, 255, 255, 0.8);
  --shadow-deep: rgba(13, 27, 42, 0.25);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-light: rgba(13, 27, 42, 0.08);
  
  /* 渐变定义 */
  --gradient-primary: linear-gradient(135deg, var(--primary-business-blue) 0%, var(--primary-deep-blue) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
}

/* 侧边栏 */
.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #1B365D 0%, #0D1B2A 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
  box-shadow: 2px 0 8px var(--shadow-light);
  transition: width 0.3s ease;
  /* 轻微增加宽度以改善导航体验 */
}

.sidebar.collapsed {
  width: 64px;
}

/* Logo区域 */
.logo-area {
  padding: 20px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  /* 减少上下内边距，提高空间利用率 */
}

.logo {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #E8B86D 0%, #B8C5D1 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 28px;
  color: #0D1B2A;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
  transition: opacity 0.3s ease;
  letter-spacing: 1px;
}

.sidebar.collapsed .logo-text {
  opacity: 0;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
  /* 减少内边距，让菜单项更紧凑 */
}

.nav-item {
  position: relative;
  margin: 4px 16px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-left: 3px solid #E8B86D;
  backdrop-filter: blur(10px);
}

.nav-icon {
  /* width: 20px;
  height: 20px; */
  margin-right: 12px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
  opacity: 0;
}

.nav-arrow {
  margin-left: auto;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.nav-arrow-expanded {
  transform: rotate(90deg);
}

/* 子菜单样式 */
.nav-submenu {
  margin-left: 20px;
  margin-top: 4px;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
}

.nav-sub-link {
  padding: 8px 16px;
  font-size: 13px;
}

.nav-sub-link .nav-icon {
  /* width: 16px;
  height: 16px; */
  margin-right: 8px;
}

.nav-arrow-expanded {
  transform: rotate(90deg);
  transition: transform 0.3s ease;
}

/* 客服联系区域 */
.customer-service {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 8px;
  margin: 8px 16px 16px 16px;
}

.customer-service:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.customer-service:active {
  transform: translateY(0);
}

.service-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.customer-service:hover .service-icon {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.service-details {
  flex: 1;
}

.service-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
  opacity: 0.95;
  margin: 0 0 2px 0;
}

.service-desc {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
}

.service-arrow {
  font-size: 16px;
  opacity: 0.6;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.customer-service:hover .service-arrow {
  opacity: 1;
  transform: translateX(2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
}
</style>