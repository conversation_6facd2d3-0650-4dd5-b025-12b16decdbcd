<template>
  <div class="app-container">
    <!-- 侧边栏 -->
    <Sidebar class="sidebar" :class="{ collapsed: sidebarCollapsed }" />

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部栏 -->
      <Header class="header" />

      <!-- 内容区域 -->
      <main class="content-area">
        <router-view />
      </main>
    </div>

    <!-- 套餐过期提醒弹窗 -->
    <ExpirationReminderDialog
      v-model="showReminderDialog"
      :remaining-days="userInfo?.company_info?.remaining_days || 0"
      :expired-at="userInfo?.company_info?.expired_at || ''"
      :version-name="userInfo?.version_info?.version_name || ''"
      @contact-service="handleContactService"
      @remind-later="handleRemindLater"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, provide, onMounted } from 'vue'
import Header from '@/components/Header.vue'
import Sidebar from '@/components/Sidebar.vue'
import ExpirationReminderDialog from '@/components/ExpirationReminderDialog.vue'
import { useExpirationReminder } from '@/composables/useExpirationReminder'
import { useAuthStore } from '@/stores/auth'

const sidebarCollapsed = ref(false)
const authStore = useAuthStore()

// 套餐过期提醒功能
const {
  showReminderDialog,
  userInfo,
  checkExpirationReminder,
  handleContactService,
  handleRemindLater
} = useExpirationReminder()

// 提供给子组件使用的方法
// 提供给Header组件调用的侧边栏切换方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 为Header组件提供切换方法
provide('toggleSidebar', toggleSidebar)

// 组件挂载后检查套餐过期提醒
onMounted(async () => {
  // 确保用户信息已加载
  if (!authStore.user) {
    await authStore.initUserInfo()
  }

  // 检查是否需要显示过期提醒
  if (authStore.user) {
    checkExpirationReminder(authStore.user as any)
  }
})

// 暴露给全局使用
defineExpose({
  toggleSidebar
})
</script>

<style scoped>
/* CSS变量定义 - 商务典雅蓝色主题系统 */
:root {
  /* 主要蓝色系 - 深邃商务蓝 */
  --primary-deep-blue: #0D1B2A;
  --primary-business-blue: #1B365D;
  --primary-steel-blue: #415A77;
  
  /* 辅助蓝色系 - 钢铁蓝与淡雅蓝灰 */
  --secondary-steel-blue: #778DA9;
  --secondary-elegant-blue: #E0E1DD;
  --secondary-light-blue-gray: #F1F3F4;
  
  /* 点缀色系 - 精致金属色 */
  --accent-platinum: #C7D2DD;
  --accent-warm-silver: #B8C5D1;
  --accent-soft-gold: #E8B86D;
  
  /* 状态色系 */
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  /* 文字颜色系统 */
  --text-primary: #0D1B2A;
  --text-secondary: #415A77;
  --text-light: #778DA9;
  --text-muted: #B8C5D1;
  
  /* 背景和效果 */
  --glass-background: rgba(255, 255, 255, 0.92);
  --glass-border: rgba(255, 255, 255, 0.8);
  --shadow-deep: rgba(13, 27, 42, 0.25);
  --shadow-medium: rgba(13, 27, 42, 0.15);
  --shadow-light: rgba(13, 27, 42, 0.08);
  
  /* 渐变定义 */
  --gradient-primary: linear-gradient(135deg, var(--primary-business-blue) 0%, var(--primary-deep-blue) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-soft-gold) 0%, var(--accent-warm-silver) 100%);
}

/* 系统容器 */
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  /* 确保容器占满整个视窗，无额外边距 */
}

/* 侧边栏 */
.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #1B365D 0%, #0D1B2A 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
  box-shadow: 2px 0 8px var(--shadow-light);
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 64px;
}

/* 主内容区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部栏 */
.header {
  height: 56px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px var(--shadow-light);
  /* 减少高度和内边距以提高空间利用率 */
}

/* 内容区域 */
.content-area {
  flex: 1;
  overflow: auto;
  background: #f8f9fa;
  /* 移除默认内边距，让各页面组件自行控制间距 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease, width 0.3s ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    width: 100%;
  }
}
</style>