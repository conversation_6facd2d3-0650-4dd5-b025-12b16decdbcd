// 企业信息类型
export interface CompanyInfo {
  id: number
  company_name: string
  mobile: string
  status: number
  buy_at: string
  expired_at: string
  remaining_days: number
  is_expired: boolean
  show_expiration_reminder: boolean
  shop_nums: number
  buy_year: number
}

// 版本信息类型
export interface VersionInfo {
  id: number
  version_name: string
  product_price: number
  continue_price: number
  limit_nums: number
  remark: string
}

// 用户相关类型定义
export interface User {
  id: number
  username: string
  nickname: string
  avatar?: string
  role_id: number
  role_name: string
  status: number
  created_at: string
  login_time?: string
  permissions?: string[] // 用户权限标识列表
  menus?: MenuInfo[] // 用户可访问的菜单列表
  company_info?: CompanyInfo // 企业信息
  version_info?: VersionInfo // 版本信息
}

// 个人资料类型
export interface ProfileData {
  id: number
  nickname: string
  username: string
  avatar?: string
  role_name: string
  login_time?: string
  login_ip?: string
  status: number
}

// 个人资料表单
export interface ProfileForm {
  id: number | null
  nickname: string
  username: string
  password: string
  role_name: string
}

// 登录表单
export interface LoginForm {
  username: string
  password: string
  captcha: string
  remember: boolean
}

// 注册表单
export interface RegisterForm {
  companyName: string
  contactName: string
  phone: string
  smsCode: string
  password: string
  confirmPassword: string
  agreement: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 登录响应
export interface LoginResponse {
  token: string
  user: User
}

// 菜单信息类型
export interface MenuInfo {
  id: number
  menu_name: string
  menu_sign: string
  menu_img: string
  pid: number
  is_show?: number
  sort?: number
  children?: MenuInfo[]
}

// 权限信息类型
export interface Permission {
  id: number
  permission_name: string
  permission_key: string
  parent_id: number
  level: number
  sort: number
  children?: Permission[]
}

// 用户权限信息
export interface UserPermissions {
  menus: MenuInfo[]
  permissions: string[]
}

// 权限检查结果
export interface PermissionCheckResult {
  hasPermission: boolean
  reason?: string
}