/**
 * 模拟权限数据，用于测试权限控制功能
 */

// 权限定义
export const PERMISSIONS = {
  // 门店管理
  SHOP_MANAGE: 'shop_manage',
  
  // 管理员管理
  ADMIN_MANAGE: 'admin_manage',
  ADMIN_ADD: 'admin_add',
  ADMIN_EDIT: 'admin_edit',
  ADMIN_DELETE: 'admin_delete',
  
  // 角色管理
  ROLE_MANAGE: 'role_manage',
  ROLE_ADD: 'role_add',
  ROLE_EDIT: 'role_edit',
  ROLE_DELETE: 'role_delete',
  ROLE_PERMISSION_ASSIGN: 'role_permission_assign',
  
  // 数据大屏（无需权限）
  DASHBOARD: 'dashboard'
} as const

// 角色权限配置
export const ROLE_PERMISSIONS = {
  // 超级管理员 - 拥有所有权限
  SUPER_ADMIN: [
    PERMISSIONS.SHOP_MANAGE,
    PERMISSIONS.ADMIN_MANAGE,
    PERMISSIONS.ADMIN_ADD,
    PERMISSIONS.ADMIN_EDIT,
    PERMISSIONS.ADMIN_DELETE,
    PERMISSIONS.ROLE_MANAGE,
    PERMISSIONS.ROLE_ADD,
    PERMISSIONS.ROLE_EDIT,
    PERMISSIONS.ROLE_DELETE,
    PERMISSIONS.ROLE_PERMISSION_ASSIGN
  ],
  
  // 门店管理员 - 只有门店管理权限
  SHOP_ADMIN: [
    PERMISSIONS.SHOP_MANAGE
  ],
  
  // 角色管理员 - 只有角色相关权限
  ROLE_ADMIN: [
    PERMISSIONS.ROLE_MANAGE,
    PERMISSIONS.ROLE_ADD,
    PERMISSIONS.ROLE_EDIT,
    PERMISSIONS.ROLE_DELETE,
    PERMISSIONS.ROLE_PERMISSION_ASSIGN
  ],
  
  // 普通用户 - 无特殊权限
  USER: []
} as const

// 模拟菜单数据
export const MOCK_MENUS = [
  {
    id: 1,
    menu_name: '数据大屏',
    menu_sign: 'dashboard',
    menu_img: '📊',
    pid: 0,
    is_show: 1,
    sort: 1
  },
  {
    id: 2,
    menu_name: '门店管理',
    menu_sign: 'shop_manage',
    menu_img: '🏪',
    pid: 0,
    is_show: 1,
    sort: 2
  },
  {
    id: 3,
    menu_name: '权限管理',
    menu_sign: '',
    menu_img: '🔐',
    pid: 0,
    is_show: 1,
    sort: 3,
    children: [
      {
        id: 4,
        menu_name: '管理员管理',
        menu_sign: 'admin_manage',
        menu_img: '👤',
        pid: 3,
        is_show: 1,
        sort: 1
      },
      {
        id: 5,
        menu_name: '角色管理',
        menu_sign: 'role_manage',
        menu_img: '🏷️',
        pid: 3,
        is_show: 1,
        sort: 2
      }
    ]
  }
]

/**
 * 根据角色获取权限列表
 */
export function getPermissionsByRole(roleName: string): string[] {
  switch (roleName) {
    case '超级管理员':
      return ROLE_PERMISSIONS.SUPER_ADMIN
    case '门店管理员':
      return ROLE_PERMISSIONS.SHOP_ADMIN
    case '角色管理员':
      return ROLE_PERMISSIONS.ROLE_ADMIN
    default:
      return ROLE_PERMISSIONS.USER
  }
}

/**
 * 模拟用户权限数据
 */
export function mockUserPermissions(roleName: string = '超级管理员') {
  return {
    permissions: getPermissionsByRole(roleName),
    menus: MOCK_MENUS
  }
}

/**
 * 设置模拟权限到用户信息中
 */
export function setMockPermissions(authStore: any, roleName: string = '超级管理员') {
  if (authStore.user) {
    const mockData = mockUserPermissions(roleName)
    authStore.user.permissions = mockData.permissions
    authStore.user.menus = mockData.menus
    authStore.user.role_name = roleName
    
    // 更新localStorage
    localStorage.setItem('company_user_info', JSON.stringify(authStore.user))
    
    console.log(`🔧 已设置模拟权限 - 角色: ${roleName}`, mockData.permissions)
  }
}
