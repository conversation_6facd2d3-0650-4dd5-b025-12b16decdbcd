import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPassword.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/package-expired',
    name: 'PackageExpired',
    component: () => import('@/views/PackageExpired.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: 'DashboardLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { 
          title: '数据大屏',
          requiresAuth: true 
        }
      }
    ]
  },
  {
    path: '/shops',
    name: 'ShopsLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'shops',
        component: () => import('@/views/Shops.vue'),
        meta: {
          title: '门店管理',
          requiresAuth: true,
          requiredPermission: 'shop_manage'
        }
      }
    ]
  },
  {
    path: '/permissions',
    name: 'PermissionsLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'permissions',
        redirect: '/permissions/admin'
      },
      {
        path: 'admin',
        name: 'permissions-admin',
        component: () => import('@/views/PermissionsAdmin.vue'),
        meta: {
          title: '管理员管理',
          requiresAuth: true,
          requiredPermission: 'admin_manage'
        }
      },
      {
        path: 'role',
        name: 'permissions-role',
        component: () => import('@/views/PermissionsRole.vue'),
        meta: {
          title: '角色管理',
          requiresAuth: true,
          requiredPermission: 'role_manage'
        }
      }
    ]
  },
  {
    path: '/permission-test',
    name: 'PermissionTestLayout',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'permission-test',
        component: () => import('@/views/PermissionTest.vue'),
        meta: {
          title: '权限测试',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 检查是否需要登录
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // 如果已登录用户访问非登录页面的公开页面，重定向到首页
  if (!to.meta.requiresAuth && authStore.isAuthenticated && to.path !== '/login') {
    next('/dashboard')
    return
  }

  // 检查权限
  if (to.meta.requiresAuth && to.meta.requiredPermission) {
    // 确保用户信息已加载
    if (!authStore.user?.permissions) {
      try {
        await authStore.loadUserPermissions()
      } catch (error) {
        console.error('加载用户权限失败:', error)
        next('/login')
        return
      }
    }

    // 检查是否有所需权限
    const hasPermission = authStore.user?.permissions?.includes(to.meta.requiredPermission as string)
    if (!hasPermission) {
      // 没有权限，重定向到首页或显示无权限页面
      next('/dashboard')
      return
    }
  }

  next()
})

export default router