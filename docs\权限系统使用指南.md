# Webman多租户SaaS权限系统使用指南

## 📖 概述

本权限系统为Webman框架设计的企业级多租户SaaS权限控制解决方案，提供完整的权限验证、缓存优化、多租户数据隔离等功能。

## 🏗️ 系统架构

### 核心组件
- **PermissionService**: 核心权限验证服务
- **PermissionCacheService**: 权限缓存优化服务
- **RoutePermissionService**: 路由权限映射服务
- **Auth中间件**: 自动权限验证中间件
- **CompanyBaseController**: 增强权限检查的基础控制器

### 多租户隔离
- 公司级别（Company）：管理多个门店和用户
- 门店级别（Shop）：具体业务操作
- 管理员级别：不同角色权限分配

## 🚀 快速开始

### 1. 在控制器中使用权限检查

```php
<?php
namespace app\shop\controller;

use app\controller\CompanyBaseController;
use support\Request;
use support\Response;

class CsGoodsController extends CompanyBaseController
{
    protected $modelName = 'CsGoods';
    protected $validateName = 'CsGoodsValidate';

    /**
     * 商品列表（自动权限验证）
     */
    public function index(Request $request): Response
    {
        // 中间件已自动验证goods_list权限
        // 无需手动检查权限
        return parent::index($request);
    }

    /**
     * 新增商品（手动权限检查）
     */
    public function addPost(Request $request): Response
    {
        // 方式1：检查权限后继续执行
        if (!$this->checkPermission('goods_add')) {
            return fail('权限不足，无法添加商品', 403);
        }

        // 方式2：要求权限（无权限抛异常）
        $this->requirePermission('goods_add');

        return parent::addPost($request);
    }

    /**
     * 批量操作（批量权限检查）
     */
    public function batchOperation(Request $request): Response
    {
        // 检查多个权限
        $permissions = $this->checkPermissions([
            'goods_edit',
            'goods_delete',
            'goods_status'
        ]);

        if (!$permissions['goods_edit']) {
            return fail('没有编辑权限', 403);
        }

        // 执行批量操作...
        return success('批量操作完成');
    }

    /**
     * 高级功能（需要超级管理员权限）
     */
    public function advancedOperation(Request $request): Response
    {
        // 要求超级管理员权限
        $this->requireSuperAdmin();

        // 执行高级操作...
        return success('高级操作完成');
    }

    /**
     * 权限信息接口
     */
    public function getPermissionInfo(Request $request): Response
    {
        // 获取当前用户权限
        $userPermissions = $this->getUserPermissions();
        
        // 获取可访问菜单
        $accessibleMenus = $this->getUserAccessibleMenus(1);
        
        return success([
            'permissions' => $userPermissions,
            'menus' => $accessibleMenus,
            'is_super_admin' => $this->isSuperAdmin()
        ]);
    }
}
```

### 2. 角色权限管理

```php
<?php
namespace app\company\controller;

use app\controller\CompanyBaseController;
use support\Request;
use support\Response;

class CsCompanyAdminRoleController extends CompanyBaseController
{
    protected $modelName = 'CsCompanyAdminRole';
    protected $validateName = 'CsCompanyAdminRoleValidate';

    /**
     * 更新角色权限后清除缓存
     */
    public function editPost(Request $request): Response
    {
        $result = parent::editPost($request);
        
        if ($result->getBody() && json_decode($result->getBody())->code === 1) {
            // 权限更新成功，清除相关缓存
            $roleId = $request->post('id');
            $this->clearPermissionCache(null, $roleId);
        }
        
        return $result;
    }
}
```

### 3. 自定义权限验证

```php
<?php
namespace app\service;

use app\service\PermissionService;

class CustomPermissionService extends PermissionService
{
    /**
     * 检查时间范围权限
     */
    public function checkTimeBasedPermission(int $companyId, int $adminId, string $menuSign, string $timeRange): bool
    {
        // 先检查基本权限
        if (!$this->checkPermission($companyId, $adminId, 0, $menuSign)) {
            return false;
        }

        // 检查时间范围权限
        $userPermissions = $this->getUserPermissions($companyId, $adminId);
        $timePermissions = $userPermissions['time_permissions'] ?? [];
        
        return in_array($timeRange, $timePermissions);
    }

    /**
     * 检查数据权限（只能查看自己创建的数据）
     */
    public function checkDataOwnership(int $companyId, int $adminId, int $dataCreatorId): bool
    {
        // 超级管理员可以查看所有数据
        if ($this->isSuperAdmin($adminId)) {
            return true;
        }

        // 检查是否为数据创建者
        return $adminId === $dataCreatorId;
    }
}
```

## 🔧 高级功能

### 1. 权限缓存管理

```php
// 手动清除权限缓存
$permissionService = new PermissionService();

// 清除指定用户权限缓存
$permissionService->clearPermissionCache($companyId, $adminId);

// 清除指定角色的所有用户权限缓存
$permissionService->clearPermissionCache($companyId, null, $roleId);

// 清除整个公司的权限缓存
$permissionService->clearPermissionCache($companyId);
```

### 2. 路由权限映射

```php
// 添加自定义路由权限映射
$routeService = new RoutePermissionService();

// 添加路由映射
$routeService->addRoutePermissionMapping(
    'CsCustomController',
    'customAction',
    'custom_permission'
);

// 添加公共路由（不需要权限检查）
$routeService->addPublicRoute('CsPublicController', 'publicAction');
```

### 3. 权限预热

```php
// 系统启动时预热权限缓存
$cacheService = new PermissionCacheService();
$cacheService->warmupPermissionCache($companyId);
```

## 📊 权限数据结构

### 权限菜单表（cs_menus）
```sql
CREATE TABLE cs_menus (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pid INT NOT NULL DEFAULT 0,
    menu_name VARCHAR(50) NOT NULL,
    menu_sign VARCHAR(100) NOT NULL,
    menu_type TINYINT NOT NULL DEFAULT 1,  -- 0:平台 1:公司 2:门店
    is_show TINYINT NOT NULL DEFAULT 1,
    sort INT NOT NULL DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 公司管理员角色表（cs_company_admin_roles）
```sql
CREATE TABLE cs_company_admin_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL,
    role_permission TEXT,  -- 逗号分隔的菜单ID或'*'表示全权限
    company_id INT NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 🔐 权限标识命名规范

### 标准格式
```
模块名_操作类型
```

### 示例
```php
// 会员管理
'member_add'       // 新增会员
'member_edit'      // 编辑会员
'member_delete'    // 删除会员
'member_status'    // 状态切换
'member_recharge'  // 会员充值

// 商品管理
'goods_add'        // 新增商品
'goods_edit'       // 编辑商品
'goods_delete'     // 删除商品
'goods_status'     // 状态切换
'goods_sort'       // 排序

// 订单管理
'order_detail'     // 订单详情
'order_cancel'     // 取消订单
'order_refund'     // 订单退款

// 财务报表
'finance_report_view'     // 查看报表
'finance_report_export'   // 导出报表
'finance_report_print'    // 打印报表
```

## 🛡️ 安全最佳实践

### 1. 权限检查原则
```php
// ❌ 错误：直接返回敏感数据
public function getUserData(Request $request): Response
{
    $data = $this->sensitiveDataService->getAllData();
    return success($data);
}

// ✅ 正确：先检查权限
public function getUserData(Request $request): Response
{
    $this->requirePermission('user_data_view');
    
    $data = $this->sensitiveDataService->getAllData();
    return success($data);
}
```

### 2. 多租户数据隔离
```php
// ❌ 错误：未考虑多租户隔离
public function getOrderList(Request $request): Response
{
    $orders = Order::all();
    return success($orders);
}

// ✅ 正确：基于公司ID过滤
public function getOrderList(Request $request): Response
{
    $this->requirePermission('order_list');
    
    $orders = Order::where('company_id', $request->company_id)->get();
    return success($orders);
}
```

### 3. 权限验证位置
```php
// ✅ 推荐：中间件自动验证（无需手动检查）
class CsGoodsController extends CompanyBaseController
{
    // Auth中间件会自动根据路由检查权限
    public function index(Request $request): Response
    {
        return parent::index($request);
    }
}

// ✅ 备选：关键操作手动验证
public function deleteGoods(Request $request): Response
{
    $this->requirePermission('goods_delete');
    
    // 执行删除操作
    return success('删除成功');
}
```

## 📈 性能优化建议

### 1. 缓存策略
- 用户权限缓存TTL：1小时
- 角色权限缓存TTL：2小时
- 菜单映射缓存TTL：24小时

### 2. 批量权限检查
```php
// ❌ 低效：多次单独检查
$canEdit = $this->checkPermission('goods_edit');
$canDelete = $this->checkPermission('goods_delete');
$canStatus = $this->checkPermission('goods_status');

// ✅ 高效：批量检查
$permissions = $this->checkPermissions([
    'goods_edit',
    'goods_delete', 
    'goods_status'
]);
```

### 3. 权限预加载
```php
// 在用户登录时预加载权限
public function login(Request $request): Response
{
    // 登录验证...
    
    // 预加载权限到缓存
    $this->cacheService->getUserPermissions($companyId, $adminId);
    
    return success('登录成功');
}
```

## 🐛 常见问题

### Q: 权限更新后不生效？
A: 需要清除相关缓存
```php
$this->clearPermissionCache($companyId, null, $roleId);
```

### Q: 如何添加新的权限？
A: 1. 在数据库中添加菜单记录 2. 更新RoutePermissionService映射 3. 清除缓存

### Q: 超级管理员权限如何判断？
A: role_id === 1 的用户为超级管理员，拥有所有权限

### Q: 如何实现数据权限（只能查看自己的数据）？
A: 在业务逻辑中添加创建者ID检查，结合权限验证使用

## 📝 更新日志

### v1.0.0 (2025-08-02)
- ✅ 完整的多租户权限系统
- ✅ Redis缓存优化
- ✅ 自动路由权限验证
- ✅ 批量权限检查
- ✅ 权限数据完整迁移
- ✅ 详细的使用文档

---

## 💡 技术支持

如有问题或建议，请查看：
1. 系统日志：`runtime/logs/webman-*.log`
2. 权限相关日志：搜索 `PERMISSION_` 关键字
3. 性能监控：Redis缓存命中率统计

本权限系统为生产环境设计，经过充分测试，可安全用于企业级应用。