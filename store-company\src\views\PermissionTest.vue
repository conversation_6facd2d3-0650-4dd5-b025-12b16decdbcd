<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">权限控制测试</h1>
      <p class="page-description">测试不同权限的显示效果</p>
    </div>

    <div class="card">
      <div class="card-header">
        <h3>当前用户信息</h3>
      </div>
      <div class="card-body">
        <div class="user-info">
          <p><strong>用户名:</strong> {{ user?.username || '未登录' }}</p>
          <p><strong>角色:</strong> {{ user?.role_name || '无角色' }}</p>
          <p><strong>权限列表:</strong></p>
          <ul v-if="user?.permissions && user.permissions.length > 0">
            <li v-for="permission in user.permissions" :key="permission">{{ permission }}</li>
          </ul>
          <p v-else>无权限</p>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <h3>权限控制测试</h3>
      </div>
      <div class="card-body">
        <div class="test-section">
          <h4>使用 v-if + hasPermission 方式</h4>
          <div class="button-group">
            <button v-if="hasPermission('shop_manage')" class="btn btn-primary">
              门店管理权限按钮
            </button>
            <button v-if="hasPermission('admin_manage')" class="btn btn-success">
              管理员管理权限按钮
            </button>
            <button v-if="hasPermission('role_manage')" class="btn btn-warning">
              角色管理权限按钮
            </button>
            <button v-if="hasPermission('role_add')" class="btn btn-info">
              新增角色权限按钮
            </button>
            <button v-if="hasPermission('role_edit')" class="btn btn-secondary">
              编辑角色权限按钮
            </button>
            <button v-if="hasPermission('role_delete')" class="btn btn-danger">
              删除角色权限按钮
            </button>
          </div>
        </div>

        <div class="test-section">
          <h4>使用 v-permission 指令方式</h4>
          <div class="button-group">
            <button v-permission="'shop_manage'" class="btn btn-primary">
              门店管理权限按钮 (指令)
            </button>
            <button v-permission="'admin_manage'" class="btn btn-success">
              管理员管理权限按钮 (指令)
            </button>
            <button v-permission="'role_manage'" class="btn btn-warning">
              角色管理权限按钮 (指令)
            </button>
            <button v-permission="['role_add', 'role_edit']" class="btn btn-info">
              新增或编辑角色权限按钮 (指令)
            </button>
          </div>
        </div>

        <div class="test-section">
          <h4>权限检查结果</h4>
          <div class="permission-results">
            <div class="result-item">
              <span>shop_manage:</span>
              <span :class="hasPermission('shop_manage') ? 'text-success' : 'text-danger'">
                {{ hasPermission('shop_manage') ? '有权限' : '无权限' }}
              </span>
            </div>
            <div class="result-item">
              <span>admin_manage:</span>
              <span :class="hasPermission('admin_manage') ? 'text-success' : 'text-danger'">
                {{ hasPermission('admin_manage') ? '有权限' : '无权限' }}
              </span>
            </div>
            <div class="result-item">
              <span>role_manage:</span>
              <span :class="hasPermission('role_manage') ? 'text-success' : 'text-danger'">
                {{ hasPermission('role_manage') ? '有权限' : '无权限' }}
              </span>
            </div>
            <div class="result-item">
              <span>role_add:</span>
              <span :class="hasPermission('role_add') ? 'text-success' : 'text-danger'">
                {{ hasPermission('role_add') ? '有权限' : '无权限' }}
              </span>
            </div>
            <div class="result-item">
              <span>role_edit:</span>
              <span :class="hasPermission('role_edit') ? 'text-success' : 'text-danger'">
                {{ hasPermission('role_edit') ? '有权限' : '无权限' }}
              </span>
            </div>
            <div class="result-item">
              <span>role_delete:</span>
              <span :class="hasPermission('role_delete') ? 'text-success' : 'text-danger'">
                {{ hasPermission('role_delete') ? '有权限' : '无权限' }}
              </span>
            </div>
          </div>
        </div>

        <div class="test-section">
          <h4>模拟权限切换</h4>
          <div class="button-group">
            <button @click="setMockPermissions(authStore, '门店管理员')" class="btn btn-outline-primary">
              模拟门店管理员权限
            </button>
            <button @click="setMockPermissions(authStore, '超级管理员')" class="btn btn-outline-success">
              模拟超级管理员权限
            </button>
            <button @click="setMockPermissions(authStore, '角色管理员')" class="btn btn-outline-warning">
              模拟角色管理员权限
            </button>
            <button @click="setMockPermissions(authStore, '普通用户')" class="btn btn-outline-danger">
              模拟普通用户（无权限）
            </button>
          </div>
          <p class="tip">💡 切换权限后，请观察侧边栏菜单和按钮的显示变化</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usePermission } from '@/composables/usePermission'
import { setMockPermissions } from '@/utils/mockPermissions'

const authStore = useAuthStore()
const { hasPermission } = usePermission()

const user = computed(() => authStore.user)

// 权限切换功能已移至 setMockPermissions 函数
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.page-description {
  color: #666;
  margin: 0;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.card-body {
  padding: 20px;
}

.user-info p {
  margin-bottom: 8px;
}

.user-info ul {
  margin: 8px 0;
  padding-left: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h4 {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-primary { background: #409eff; color: white; }
.btn-success { background: #67c23a; color: white; }
.btn-warning { background: #e6a23c; color: white; }
.btn-info { background: #909399; color: white; }
.btn-secondary { background: #606266; color: white; }
.btn-danger { background: #f56c6c; color: white; }

.btn-outline-primary { background: transparent; color: #409eff; border: 1px solid #409eff; }
.btn-outline-success { background: transparent; color: #67c23a; border: 1px solid #67c23a; }
.btn-outline-warning { background: transparent; color: #e6a23c; border: 1px solid #e6a23c; }
.btn-outline-danger { background: transparent; color: #f56c6c; border: 1px solid #f56c6c; }

.btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.permission-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.tip {
  margin-top: 10px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-left: 4px solid #409eff;
  border-radius: 4px;
  font-size: 14px;
  color: #409eff;
}
</style>
