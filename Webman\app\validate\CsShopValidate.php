<?php

namespace app\validate;

class CsShopValidate extends BaseValidate
{
    protected $rule = [
        'shop_name|店铺名称' => [
            'require',
            'chsAlphaNum',
            'min' => 1,
            'max' => 255,
            'unique' => 'app\model\CsShop,shop_name^company_id'
        ],
        'mobile|手机号码' => [
            'require',
            'mobile',
            'unique' => 'app\model\CsShop,mobile',
        ],
        'password|密码' => [
            'require',
            'alphaDash',
            'min' => 6,
            'max' => 20,
        ],
        'service_phone|服务电话' => [
            'require',
            'regex' => '/^(\d{3,4}-\d{7,8}|\d{11})$/',
        ],
        /*'status|状态' => [
            'require',
            'number',
        ],*/
        'company_id|授权公司' => [
            'require',
            'number',
        ]
    ];
}
