import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface UserInfo {
  company_info: {
    remaining_days: number
    expired_at: string
    show_expiration_reminder: boolean
    is_expired: boolean
  }
  version_info: {
    version_name: string
  }
}

export function useExpirationReminder() {
  const showReminderDialog = ref(false)
  const userInfo = ref<UserInfo | null>(null)

  /**
   * 检查是否需要显示过期提醒
   */
  const checkExpirationReminder = (userData: UserInfo) => {
    userInfo.value = userData
    
    // 如果套餐已过期，不显示提醒（应该跳转到过期页面）
    if (userData.company_info.is_expired) {
      return false
    }
    
    // 如果需要显示过期提醒
    if (userData.company_info.show_expiration_reminder) {
      // 检查今天是否已经提醒过
      const today = new Date().toDateString()
      const lastReminderDate = localStorage.getItem('expiration_reminder_date')
      
      if (lastReminderDate !== today) {
        showReminderDialog.value = true
        // 记录今天已经提醒过
        localStorage.setItem('expiration_reminder_date', today)
        return true
      }
    }
    
    return false
  }

  /**
   * 处理联系客服
   */
  const handleContactService = () => {
    // 这里可以打开客服对话框或跳转到联系页面
    ElMessage.success('正在为您转接客服...')
    
    // 示例：打开客服电话或在线客服
    // window.open('tel:************')
    // 或者显示客服联系方式弹窗
    showCustomerServiceDialog()
  }

  /**
   * 处理稍后提醒
   */
  const handleRemindLater = () => {
    // 记录用户选择稍后提醒，明天再次显示
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    localStorage.setItem('expiration_reminder_date', tomorrow.toDateString())
  }

  /**
   * 显示客服联系方式
   */
  const showCustomerServiceDialog = () => {
    ElMessage({
      message: '客服电话：************，工作时间：9:00-18:00',
      type: 'info',
      duration: 5000,
      showClose: true
    })
  }

  /**
   * 获取剩余天数的文本描述
   */
  const getRemainingDaysText = computed(() => {
    if (!userInfo.value) return ''
    
    const days = userInfo.value.company_info.remaining_days
    if (days <= 0) return '已到期'
    if (days === 1) return '明天到期'
    if (days <= 7) return `${days}天后到期`
    if (days <= 30) return `${days}天后到期`
    return `${days}天后到期`
  })

  /**
   * 获取提醒消息的紧急程度
   */
  const getReminderUrgency = computed(() => {
    if (!userInfo.value) return 'normal'
    
    const days = userInfo.value.company_info.remaining_days
    if (days <= 3) return 'urgent'
    if (days <= 7) return 'warning'
    return 'normal'
  })

  /**
   * 重置提醒状态（用于测试或管理员操作）
   */
  const resetReminderState = () => {
    localStorage.removeItem('expiration_reminder_date')
    showReminderDialog.value = false
  }

  return {
    showReminderDialog,
    userInfo,
    checkExpirationReminder,
    handleContactService,
    handleRemindLater,
    getRemainingDaysText,
    getReminderUrgency,
    resetReminderState
  }
}
