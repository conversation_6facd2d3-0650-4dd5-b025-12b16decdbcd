<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * 为store-company项目添加权限控制
     * 
     * 根据需求，只对"门店管理"和"权限管理"这两个模块添加权限控制
     * menu_type始终为1（授权商户）
     * 
     * 不需要权限控制的模块：
     * - 数据大屏
     * - 个人中心
     * - 版本信息
     * - 联系客服
     * - 退出
     */
    public function up(): void
    {
        $now = Carbon::now();
        
        // 批量插入store-company项目的权限菜单
        DB::table('cs_menus')->insert([

            // =============================================================================
            // 门店管理模块权限（假设门店管理主菜单ID为1002）
            // =============================================================================
            [
                'pid' => 1002,
                'menu_name' => '查看门店',
                'menu_sign' => 'company_shop_view',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '新增门店',
                'menu_sign' => 'company_shop_add',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '编辑门店',
                'menu_sign' => 'company_shop_edit',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '删除门店',
                'menu_sign' => 'company_shop_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店状态切换',
                'menu_sign' => 'company_shop_status',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店配置',
                'menu_sign' => 'company_shop_config',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店数据统计',
                'menu_sign' => 'company_shop_statistics',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '门店详情',
                'menu_sign' => 'company_shop_detail',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 8,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '批量操作',
                'menu_sign' => 'company_shop_batch',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 9,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1002,
                'menu_name' => '导出门店数据',
                'menu_sign' => 'company_shop_export',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 10,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // =============================================================================
            // 权限管理模块权限（假设权限管理主菜单ID为1003）
            // =============================================================================
            
            // 管理员管理子模块权限（假设管理员管理菜单ID为1031）
            [
                'pid' => 1031,
                'menu_name' => '查看管理员',
                'menu_sign' => 'company_admin_view',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '新增管理员',
                'menu_sign' => 'company_admin_add',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '编辑管理员',
                'menu_sign' => 'company_admin_edit',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '删除管理员',
                'menu_sign' => 'company_admin_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '管理员状态切换',
                'menu_sign' => 'company_admin_status',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '重置密码',
                'menu_sign' => 'company_admin_reset_password',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '管理员详情',
                'menu_sign' => 'company_admin_detail',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1031,
                'menu_name' => '批量删除管理员',
                'menu_sign' => 'company_admin_batch_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 8,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

            // 角色管理子模块权限（假设角色管理菜单ID为1032）
            [
                'pid' => 1032,
                'menu_name' => '查看角色',
                'menu_sign' => 'company_role_view',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 1,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '新增角色',
                'menu_sign' => 'company_role_add',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 2,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '编辑角色',
                'menu_sign' => 'company_role_edit',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 3,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '删除角色',
                'menu_sign' => 'company_role_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 4,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '权限分配',
                'menu_sign' => 'company_role_permission_assign',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 5,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '角色详情',
                'menu_sign' => 'company_role_detail',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 6,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '批量删除角色',
                'menu_sign' => 'company_role_batch_delete',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 7,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'pid' => 1032,
                'menu_name' => '复制角色',
                'menu_sign' => 'company_role_copy',
                'menu_type' => 1,
                'is_show' => 0,
                'sort' => 8,
                'status' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],

        ]);
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        // 删除所有新增的store-company权限菜单
        DB::table('cs_menus')->whereIn('menu_sign', [
            
            // 门店管理模块
            'company_shop_view', 'company_shop_add', 'company_shop_edit', 'company_shop_delete',
            'company_shop_status', 'company_shop_config', 'company_shop_statistics', 
            'company_shop_detail', 'company_shop_batch', 'company_shop_export',
            
            // 管理员管理模块
            'company_admin_view', 'company_admin_add', 'company_admin_edit', 'company_admin_delete',
            'company_admin_status', 'company_admin_reset_password', 'company_admin_detail', 
            'company_admin_batch_delete',
            
            // 角色管理模块
            'company_role_view', 'company_role_add', 'company_role_edit', 'company_role_delete',
            'company_role_permission_assign', 'company_role_detail', 'company_role_batch_delete', 
            'company_role_copy',
            
        ])->delete();
    }
};
